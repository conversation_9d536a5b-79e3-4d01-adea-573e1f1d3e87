{"version": 3, "file": "bundle.cjs", "sources": ["../../src/tag.js", "../../src/image.js", "../../src/tagsArray.js", "../../src/book.js", "../../src/search.js", "../../node_modules/follow-redirects/debug.js", "../../node_modules/follow-redirects/index.js", "../../node_modules/fast-fifo/index.js", "../../node_modules/fast-fifo/fixed-size.js", "../../node_modules/b4a/index.js", "../../node_modules/text-decoder/lib/pass-through-decoder.js", "../../node_modules/text-decoder/lib/utf8-decoder.js", "../../node_modules/text-decoder/index.js", "../../node_modules/streamx/index.js", "../../node_modules/tar-stream/headers.js", "../../node_modules/tar-stream/extract.js", "../../node_modules/tar-stream/constants.js", "../../node_modules/tar-stream/pack.js", "../../node_modules/wrappy/wrappy.js", "../../node_modules/once/once.js", "../../node_modules/end-of-stream/index.js", "../../node_modules/pump/index.js", "../../node_modules/tar-fs/index.js", "../../node_modules/@sparticuz/chromium/build/esm/helper.js", "../../node_modules/@sparticuz/chromium/build/esm/lambdafs.js", "../../node_modules/@sparticuz/chromium/build/esm/index.js", "../../node_modules/@sparticuz/chromium/build/esm/paths.esm.js", "../../src/error.js", "../../src/api.js", "../../src/options.js", "../../src/index.js"], "sourcesContent": ["/**\r\n * @module Tag\r\n */\r\n\r\n/**\r\n * Tag object from API.\r\n * @global\r\n * @typedef {object} APITag\r\n * @property {number|string} id    Tag id.\r\n * @property {string}        type  Tag type.\r\n * @property {string}        name  Tag name.\r\n * @property {number|string} count Tagged books count.\r\n * @property {string}        url   Tag URL.\r\n */\r\n\r\n/**\r\n * @typedef {object} TagTypes\r\n * @property {UnknownTagType} Unknown   Unknown tag type.\r\n * @property {TagType}        Tag       Tag tag type.\r\n * @property {TagType}        Category  Category tag type.\r\n * @property {TagType}        Artist    Artist tag type.\r\n * @property {TagType}        Parody    Parody tag type.\r\n * @property {TagType}        Character Character tag type.\r\n * @property {TagType}        Group     Group tag type.\r\n * @property {TagType}        Language  Language tag type.\r\n */\r\n\r\n/**\r\n * Class representing tag type.\r\n * @class\r\n */\r\nclass TagType {\r\n\t/**\r\n\t * @type {TagTypes}\r\n\t * @static\r\n\t * @default {}\r\n\t */\r\n\tstatic knownTypes = {};\r\n\r\n\t/**\r\n\t * Tag type name.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\ttype = null;\r\n\r\n\t/**\r\n\t * Create tag type.\r\n\t * @param {string} type Tag type.\r\n\t */\r\n\tconstructor(type) {\r\n\t\tif (type) {\r\n\t\t\tthis.type = type;\r\n\t\t\tthis.constructor.knownTypes[type] = this;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Check if this tag type is unknown.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isKnown() {\r\n\t\treturn !(this instanceof UnknownTagType);\r\n\t}\r\n\r\n\t/**\r\n\t * Tag type name.\r\n\t * @returns {string}\r\n\t */\r\n\ttoString() {\r\n\t\treturn this.type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing unknown tag type.\r\n * @class\r\n * @extends TagType\r\n */\r\nclass UnknownTagType extends TagType {\r\n\t/**\r\n\t * Create unknown tag type.\r\n\t * @param {string} [type=\"unknown\"] Unknown tag type name.\r\n\t */\r\n\tconstructor(type = 'unknown') {\r\n\t\tsuper(null);\r\n\t\tthis.type = type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing tag.\r\n * @class\r\n */\r\nclass Tag {\r\n\t/**\r\n\t * Tag types.\r\n\t * @type {TagTypes}\r\n\t * @static\r\n\t */\r\n\tstatic types = {\r\n\t\tUnknown  : new UnknownTagType(), // Symbol('unknown')\r\n\t\tTag      : new TagType('tag'),\r\n\t\tCategory : new TagType('category'),\r\n\t\tArtist   : new TagType('artist'),\r\n\t\tParody   : new TagType('parody'),\r\n\t\tCharacter: new TagType('character'),\r\n\t\tGroup    : new TagType('group'),\r\n\t\tLanguage : new TagType('language'),\r\n\r\n\t\t/**\r\n\t\t * Known tag types.\r\n\t\t * @type {TagTypes}\r\n\t\t */\r\n\t\tknown: TagType.knownTypes,\r\n\r\n\t\t/**\r\n\t\t * Get tag type class instance by name.\r\n\t\t * @param {string} type Tag type.\r\n\t\t * @returns {TagType|UnknownTagType} Tag type class instance.\r\n\t\t */\r\n\t\tget(type) {\r\n\t\t\tlet known;\r\n\t\t\tif ('string' === typeof type)\r\n\t\t\t\ttype = type.toLowerCase();\r\n\t\t\treturn ((known = this.known[type]))\r\n\t\t\t\t? known\r\n\t\t\t\t: new UnknownTagType(type);\r\n\t\t},\r\n\t};\r\n\r\n\t/**\r\n\t * Warp tag object with Tag class instance.\r\n\t * @param {APITag|Tag} tag Tag to wrap.\r\n\t * @returns {Tag} Tag.\r\n\t * @static\r\n\t */\r\n\tstatic get(tag) {\r\n\t\tif (!(tag instanceof this))\r\n\t\t\ttag = new this({\r\n\t\t\t\tid   : +tag.id,\r\n\t\t\t\ttype : tag.type,\r\n\t\t\t\tname : tag.name,\r\n\t\t\t\tcount: +tag.count,\r\n\t\t\t\turl  : tag.url,\r\n\t\t\t});\r\n\t\treturn tag;\r\n\t}\r\n\r\n\t/**\r\n\t * Tag ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tid = 0;\r\n\r\n\t/**\r\n\t * Tag type.\r\n\t * @type {TagType|UnknownTagType}\r\n\t * @default TagTypes.Unknown\r\n\t */\r\n\ttype = this.constructor.types.Unknown;\r\n\r\n\t/**\r\n\t * Tag name.\r\n\t * @type {string}\r\n\t * @default \"\"\r\n\t */\r\n\tname = '';\r\n\r\n\t/**\r\n\t * Count of books tagged with this tag.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tcount = 0;\r\n\r\n\t/**\r\n\t * Tag URL.\r\n\t * @type {string}\r\n\t * @default \"\"\r\n\t */\r\n\turl = '';\r\n\r\n\t/**\r\n\t * Create tag.\r\n\t * @param {object}         [params]                       Tag parameters.\r\n\t * @param {number}         [params.id=0]                  Tag id.\r\n\t * @param {string|TagType} [params.type=TagTypes.Unknown] Tag type.\r\n\t * @param {string}         [params.name=\"\"]               Tag name.\r\n\t * @param {number}         [params.count=0]               Tagged books count.\r\n\t * @param {string}         [params.url=\"\"]                Tag URL.\r\n\t */\r\n\tconstructor({\r\n\t\tid    = 0,\r\n\t\ttype  = this.constructor.types.Unknown,\r\n\t\tname  = '',\r\n\t\tcount = 0,\r\n\t\turl   = '',\r\n\t} = {}) {\r\n\t\tObject.assign(this, {\r\n\t\t\tid,\r\n\t\t\ttype: type instanceof TagType\r\n\t\t\t\t? type\r\n\t\t\t\t: this.constructor.types.get(type),\r\n\t\t\tname,\r\n\t\t\tcount,\r\n\t\t\turl,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Compare this to given one.\r\n\t * By default tags with different id will return false.\r\n\t * If you want to check whatever tag has any of properties from another tag pass `'any'` to `strict` parameter.\r\n\t * @param {string|Tag} tag                Tag to compare with.\r\n\t * @param {boolean|string} [strict=false] Whatever all parameters must be the same.\r\n\t * @returns {boolean} Whatever tags are equal.\r\n\t */\r\n\tcompare(tag, strict = false) {\r\n\t\ttag = this.constructor.get(tag);\r\n\t\tif (strict === 'any')\r\n\t\t\tstrict = false;\r\n\t\telse if (this.id !== tag.id)\r\n\t\t\treturn false;\r\n\r\n\t\treturn !![\r\n\t\t\t'id',\r\n\t\t\t'type',\r\n\t\t\t'name',\r\n\t\t\t'count',\r\n\t\t\t'url',\r\n\t\t].map(\r\n\t\t\tprop => tag[prop] === this[prop]\r\n\t\t).reduce(\r\n\t\t\t(accum, current) => strict\r\n\t\t\t\t? accum * current\r\n\t\t\t\t: accum + current\r\n\t\t);\r\n\t}\r\n\r\n\t/**\r\n\t * Get tag name or tag name with count of tagged books.\r\n\t * @param {?boolean} [includeCount=false] Include count.\r\n\t * @returns {string}\r\n\t */\r\n\ttoString(includeCount = false) {\r\n\t\treturn this.name + (includeCount\r\n\t\t\t? ` (${this.count})`\r\n\t\t\t: '');\r\n\t}\r\n}\r\n\r\nexport {\r\n\tTag,\r\n\tTagType,\r\n\tUnknownTagType,\r\n};\r\n", "/**\r\n * @module Image\r\n */\r\n\r\nimport Book from './book';\r\n\r\n/**\r\n * Image object from API.\r\n * @global\r\n * @typedef {object} APIImage\r\n * @property {string}        t Image type.\r\n * @property {number|string} w Image width.\r\n * @property {number|string} h Image height.\r\n */\r\n\r\n/**\r\n * @typedef {object} ImageTypes\r\n * @property {TagType} JPEG JPEG image type.\r\n * @property {TagType} PNG  PNG image type.\r\n * @property {TagType} GIF  GIF image type.\r\n */\r\n\r\n/**\r\n * Class representing image type.\r\n * @class\r\n */\r\nclass ImageType {\r\n\t/**\r\n\t * @type {ImageTypes}\r\n\t * @static\r\n\t * @default {}\r\n\t */\r\n\tstatic knownTypes = {};\r\n\r\n\t/**\r\n\t * Image type name.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\ttype = null;\r\n\r\n\t/**\r\n\t * Image type extension.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\textension = null;\r\n\r\n\t/**\r\n\t * Create image type.\r\n\t * @param {string} type      Image type name.\r\n\t * @param {string} extension Image type extension.\r\n\t */\r\n\tconstructor(type, extension) {\r\n\t\tif (type) {\r\n\t\t\tthis.type = type;\r\n\t\t\tthis.constructor.knownTypes[type] = this;\r\n\t\t}\r\n\t\tthis.extension = extension;\r\n\t}\r\n\r\n\t/**\r\n\t * Whatever this tag type is unknown.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isKnown() {\r\n\t\treturn !(this instanceof UnknownImageType);\r\n\t}\r\n\r\n\t/**\r\n\t * Alias for type.\r\n\t * @type {?string}\r\n\t */\r\n\tget name() {\r\n\t\treturn this.type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing unknown image type.\r\n * @class\r\n * @extends ImageType\r\n */\r\nclass UnknownImageType extends ImageType {\r\n\t/**\r\n\t * Create unknown image type.\r\n\t * @param {string} type      Unknown image type name.\r\n\t * @param {string} extension Unknown image type extension.\r\n\t */\r\n\tconstructor(type, extension) {\r\n\t\tsuper(null, extension);\r\n\t\tthis.type = type;\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing image.\r\n * @class\r\n */\r\nclass Image {\r\n\t/**\r\n\t * Image types.\r\n\t * @type {ImageTypes}\r\n\t * @static\r\n\t */\r\n\tstatic types = {\r\n\t\tJPEG: new ImageType('jpeg', 'jpg'),\r\n\t\tPNG : new ImageType('png', 'png'),\r\n\t\tGIF : new ImageType('gif', 'gif'),\r\n\t\tWEBP: new ImageType('webp', 'webp'),\r\n\r\n\t\tUnknown: new UnknownImageType('unknown', 'unknownExt'),\r\n\r\n\t\t/**\r\n\t\t * Known image types.\r\n\t\t * @type {ImageType}\r\n\t\t */\r\n\t\tknown: ImageType.knownTypes,\r\n\r\n\t\t/**\r\n\t\t * Get image type class instance by name.\r\n\t\t * @param {string} type Image type.\r\n\t\t * @returns {ImageType|UnknownImageType} Image type class instance.\r\n\t\t */\r\n\t\tget(type) {\r\n\t\t\tlet known;\r\n\t\t\tif ('string' === typeof type) {\r\n\t\t\t\ttype = type.toLowerCase();\r\n\t\t\t\tswitch (type) {\r\n\t\t\t\t\tcase 'j':\r\n\t\t\t\t\tcase 'jpg':\r\n\t\t\t\t\tcase 'jpeg':\r\n\t\t\t\t\t\ttype = 'jpeg';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'p':\r\n\t\t\t\t\tcase 'png':\r\n\t\t\t\t\t\ttype = 'png';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'g':\r\n\t\t\t\t\tcase 'gif':\r\n\t\t\t\t\t\ttype = 'gif';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'w':\r\n\t\t\t\t\tcase 'webp':\r\n\t\t\t\t\t\ttype = 'webp';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn ((known = this.known[type]))\r\n\t\t\t\t? known\r\n\t\t\t\t: new UnknownImageType(type);\r\n\t\t},\r\n\t};\r\n\r\n\t/**\r\n\t * Parse pure image object from API into class instance.\r\n\t * @param {APIImage} image  Image object\r\n\t * @param {number}   [id=0] Image id (a.k.a. page number).\r\n\t * @returns {Image} Image instance.\r\n\t * @static\r\n\t */\r\n\tstatic parse(image, id = 0) {\r\n\t\tlet {\r\n\t\t\tt: type,\r\n\t\t\tw: width,\r\n\t\t\th: height,\r\n\t\t} = image;\r\n\r\n\t\treturn new this({\r\n\t\t\ttype,\r\n\t\t\twidth : +width,\r\n\t\t\theight: +height,\r\n\t\t\tid,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Image ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tid = 0;\r\n\r\n\t/**\r\n\t * Image width.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\twidth = 0;\r\n\r\n\t/**\r\n\t * Image height.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\theight = 0;\r\n\r\n\t/**\r\n\t * Image type.\r\n\t * @type {ImageType}\r\n\t * @default ImageTypes.JPEG\r\n\t */\r\n\ttype = this.constructor.types.JPEG;\r\n\r\n\t/**\r\n\t * Image parent book.\r\n\t * @type {Book}\r\n\t * @default Book.Unknown\r\n\t */\r\n\tbook = Book.Unknown;\r\n\r\n\t/**\r\n\t * Create image.\r\n\t * @param {object}           [params]                      Image parameters.\r\n\t * @param {number}           [params.id=0]                 Image ID.\r\n\t * @param {number}           [params.width=0]              Image width.\r\n\t * @param {number}           [params.height=0]             Image height.\r\n\t * @param {string|ImageType} [params.type=ImageTypes.JPEG] Image type.\r\n\t * @param {Book}             [params.book=Book.Unknown]    Image's Book.\r\n\t */\r\n\tconstructor({\r\n\t\tid     = 0,\r\n\t\twidth  = 0,\r\n\t\theight = 0,\r\n\t\ttype   = this.constructor.types.JPEG,\r\n\t\tbook   = Book.Unknown,\r\n\t} = {}) {\r\n\t\tObject.assign(this, {\r\n\t\t\tid: 'number' === typeof id\r\n\t\t\t\t? id < 1 ? 0 : id\r\n\t\t\t\t: 0,\r\n\t\t\twidth,\r\n\t\t\theight,\r\n\t\t\ttype: type instanceof ImageType\r\n\t\t\t\t? type\r\n\t\t\t\t: this.constructor.types.get(type),\r\n\t\t\tbook: book instanceof Book\r\n\t\t\t\t? book\r\n\t\t\t\t: Book.Unknown,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Whatever this image is book cover.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isCover() {\r\n\t\treturn this.id < 1;\r\n\t}\r\n\r\n\t/**\r\n\t * Image filename.\r\n\t * @type {string}\r\n\t */\r\n\tget filename() {\r\n\t\treturn `${this.isCover ? 'cover' : this.id}.${this.type.extension}`;\r\n\t}\r\n}\r\n\r\nexport default Image;\r\n", "// eslint-disable-next-line no-unused-vars\r\nimport { Tag, } from './tag';\r\n\r\n\r\n/**\r\n * Array of Tags with helper methods.\r\n * @class\r\n * @extends Array<Tag>\r\n */\r\nclass TagsArray extends Array {\r\n\tconstructor(...args) {\r\n\t\tsuper(...args);\r\n\t}\r\n\r\n\t/**\r\n\t * Get array of tags names.\r\n\t * @param {?boolean} [includeCount=false] Include count.\r\n\t * @returns {String[]}\r\n\t */\r\n\ttoNames(includeCount = false) {\r\n\t\treturn Array.from(this, tag => tag.toString(includeCount));\r\n\t}\r\n}\r\n\r\nexport default TagsArray;\r\n", "/**\r\n * @module Book\r\n */\r\n\r\nimport Image from './image';\r\nimport { Tag, } from './tag';\r\nimport TagsArray from './tagsArray';\r\n\r\nimport { TagTypes, } from '.';\r\n\r\n\r\n/**\r\n * Book object from API.\r\n * @global\r\n * @typedef {object} APIBook\r\n * @property {object}        title          Book title.\r\n * @property {string}        title.english  Book english title.\r\n * @property {string}        title.japanese Book japanese title.\r\n * @property {string}        title.pretty   Book short title.\r\n * @property {number|string} id             Book ID.\r\n * @property {number|string} media_id       Book Media ID.\r\n * @property {number|string} num_favorites  Book favours count.\r\n * @property {number|string} num_pages      Book pages count.\r\n * @property {string}        scanlator      Book scanlator.\r\n * @property {number|string} uploaded       Upload UNIX timestamp.\r\n * @property {APIImage}      cover          Book cover image.\r\n * @property {APIImage[]}    images         Book pages' images.\r\n * @property {APITag[]}      tags           Book tags.\r\n */\r\n\r\n/**\r\n * Book title.\r\n * @typedef {object} BookTitle\r\n * @property {string} english  Book english title.\r\n * @property {string} japanese Book japanese title.\r\n * @property {string} pretty   Book short title.\r\n */\r\n\r\n/**\r\n * Class representing Book.\r\n * @class\r\n */\r\nclass Book {\r\n\t/**\r\n\t * Unknown book instance.\r\n\t * @type {UnknownBook}\r\n\t * @static\r\n\t */\r\n\tstatic Unknown;\r\n\r\n\t/**\r\n\t * UnknownBook class.\r\n\t * @type {UnknownBook}\r\n\t * @static\r\n\t */\r\n\tstatic UnknownBook;\r\n\r\n\t/**\r\n\t * Parse book object into class instance.\r\n\t * @param {APIBook} book Book.\r\n\t * @returns {Book} Book instance.\r\n\t * @static\r\n\t */\r\n\tstatic parse(book) {\r\n\t\treturn new this({\r\n\t\t\ttitle    : book.title,\r\n\t\t\tid       : +book.id,\r\n\t\t\tmedia    : +book.media_id,\r\n\t\t\tfavorites: +book.num_favorites,\r\n\t\t\tscanlator: book.scanlator,\r\n\t\t\tuploaded : new Date(+book.upload_date * 1000),\r\n\t\t\ttags     : TagsArray.from(book.tags, tag => Tag.get(tag)),\r\n\t\t\tcover    : Image.parse(book.images.cover),\r\n\t\t\tpages    : book.images.pages.map(\r\n\t\t\t\t(image, id) => Image.parse(image, ++id)\r\n\t\t\t),\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Book title.\r\n\t * @type {BookTitle}\r\n\t */\r\n\ttitle = {\r\n\t\tenglish : '',\r\n\t\tjapanese: '',\r\n\t\tpretty  : '',\r\n\t};\r\n\r\n\t/**\r\n\t * Book ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tid = 0;\r\n\r\n\t/**\r\n\t * Book Media ID.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tmedia = 0;\r\n\r\n\t/**\r\n\t * Book favours count.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tfavorites = 0;\r\n\r\n\t/**\r\n\t * Book scanlator.\r\n\t * @type {string}\r\n\t * @default ''\r\n\t */\r\n\tscanlator = '';\r\n\r\n\t/**\r\n\t * Book upload date.\r\n\t * @type {Date}\r\n\t * @default new Date(0)\r\n\t */\r\n\tuploaded = new Date(0);\r\n\r\n\t/**\r\n\t * Book tags.\r\n\t * @type {TagsArray}\r\n\t * @default []\r\n\t */\r\n\ttags = new TagsArray();\r\n\r\n\t/**\r\n\t * Book cover.\r\n\t * @type {Image}\r\n\t */\r\n\tcover = new Image({ id: 0, book: this, });\r\n\r\n\t/**\r\n\t * Book pages.\r\n\t * @type {Image[]}\r\n\t * @default []\r\n\t */\r\n\tpages = [];\r\n\r\n\t/**\r\n\t * Create book.\r\n\t * @param {object}          [params]              Book parameters.\r\n\t * @param {BookTitle}       [params.title]        Book title.\r\n\t * @param {number}          [params.id=0]         Book ID.\r\n\t * @param {number}          [params.media=0]      Book Media ID.\r\n\t * @param {number}          [params.favorites=0]  Book favours count.\r\n\t * @param {string}          [params.scanlator=''] Book scanlator.\r\n\t * @param {Date}            [params.uploaded]     Book upload date.\r\n\t * @param {Tag[]|TagsArray} [params.tags=[]]      Book tags.\r\n\t * @param {Image}           [params.cover]        Book cover.\r\n\t * @param {Image[]}         [params.pages=[]]     Book pages.\r\n\t */\r\n\tconstructor({\r\n\t\ttitle     = {\r\n\t\t\tenglish : '',\r\n\t\t\tjapanese: '',\r\n\t\t\tpretty  : '',\r\n\t\t},\r\n\t\tid        = 0,\r\n\t\tmedia     = 0,\r\n\t\tfavorites = 0,\r\n\t\tscanlator = '',\r\n\t\tuploaded  = new Date(0),\r\n\t\ttags      = new TagsArray(),\r\n\t\tcover     = new Image({ id: 0, book: this, }),\r\n\t\tpages     = [],\r\n\t} = {}) {\r\n\t\tthis.setCover(cover);\r\n\r\n\t\tif (Array.isArray(pages))\r\n\t\t\tpages.forEach(this.pushPage.bind(this));\r\n\r\n\t\tif (Array.isArray(tags))\r\n\t\t\ttags.forEach(this.pushTag.bind(this));\r\n\r\n\t\tObject.assign(this, {\r\n\t\t\ttitle,\r\n\t\t\tid,\r\n\t\t\tmedia,\r\n\t\t\tfavorites,\r\n\t\t\tscanlator,\r\n\t\t\tuploaded,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Check whatever book is known.\r\n\t * @type {boolean}\r\n\t */\r\n\tget isKnown() {\r\n\t\treturn !(this instanceof UnknownBook);\r\n\t}\r\n\r\n\t/**\r\n\t * Set book cover image.\r\n\t * @param {Image} cover Image.\r\n\t * @returns {boolean} Whatever cover was set.\r\n\t * @private\r\n\t */\r\n\tsetCover(cover) {\r\n\t\tif (cover instanceof Image) {\r\n\t\t\tcover.book = this;\r\n\t\t\tthis.cover = cover;\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Push image to book pages.\r\n\t * @param {Image} page Image.\r\n\t * @returns {boolean} Whatever page was added.\r\n\t * @private\r\n\t */\r\n\tpushPage(page) {\r\n\t\tif (page instanceof Image) {\r\n\t\t\tpage.book = this;\r\n\t\t\tthis.pages.push(page);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Push tag to book tags.\r\n\t * @param {Tag} tag Tag.\r\n\t * @returns {boolean} Whatever tag was added.\r\n\t * @private\r\n\t */\r\n\tpushTag(tag) {\r\n\t\ttag = Tag.get(tag);\r\n\r\n\t\tif (!this.hasTag(tag)) {\r\n\t\t\tthis.tags.push(tag);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Check if book has certain tag.\r\n\t * @param {Tag}     tag            Tag\r\n\t * @param {boolean} [strict=false] Strict comparison.\r\n\t */\r\n\thasTag(tag, strict = true) {\r\n\t\ttag = Tag.get(tag);\r\n\r\n\t\treturn this.tags.some(elem => elem.compare(tag, strict));\r\n\t}\r\n\r\n\t/**\r\n\t * Check if book has any tags with certain properties.\r\n\t * @param {object|Tag} tag Tag.\r\n\t */\r\n\thasTagWith(tag) {\r\n\t\treturn this.hasTag(tag, 'any');\r\n\t}\r\n\r\n\t/**\r\n\t * Get any tags with certain properties.\r\n\t * @param {object|Tag} tag Tag.\r\n\t * @returns {TagsArray}\r\n\t */\r\n\tgetTagsWith(tag) {\r\n\t\ttag = Tag.get(tag);\r\n\r\n\t\treturn this.tags.filter(elem => elem.compare(tag, 'any'));\r\n\t}\r\n\r\n\t/**\r\n\t * Pure tags (with type {TagType.Tag}).\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget pureTags() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Tag, });\r\n\t}\r\n\r\n\t/**\r\n\t * Category tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget categories() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Category, });\r\n\t}\r\n\r\n\t/**\r\n\t * Artist tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget artists() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Artist, });\r\n\t}\r\n\r\n\t/**\r\n\t * Parody tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget parodies() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Parody, });\r\n\t}\r\n\r\n\t/**\r\n\t * Character tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget characters() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Character, });\r\n\t}\r\n\r\n\t/**\r\n\t * Group tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget groups() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Group, });\r\n\t}\r\n\r\n\t/**\r\n\t * Language tags.\r\n\t * @type {Tag[]}\r\n\t */\r\n\tget languages() {\r\n\t\treturn this.getTagsWith({ type: TagTypes.Language, });\r\n\t}\r\n}\r\n\r\n/**\r\n * Class representing unknown book.\r\n * @class\r\n * @extends Book\r\n */\r\nclass UnknownBook extends Book {\r\n\t/**\r\n\t * Create unknown book.\r\n\t */\r\n\tconstructor() {\r\n\t\tsuper({});\r\n\t}\r\n}\r\n\r\nBook.UnknownBook = UnknownBook;\r\nBook.Unknown = new UnknownBook();\r\n\r\nexport default Book;\r\n", "/**\r\n * @module Search\r\n */\r\n\r\nimport API from './api';\r\nimport Book from './book';\r\nimport { Tag, } from './tag';\r\n\r\n\r\n/**\r\n * Search object from API.\r\n * @global\r\n * @typedef {object} APISearch\r\n * @property {APIBook[]}     result    Search results.\r\n * @property {number|string} num_pages Number of search pages available.\r\n * @property {number|string} per_page  Number of books per page.\r\n */\r\n\r\n\r\n/**\r\n * @typedef {''|'popular'|'popular-week'|'popular-today'|'popular-month'} SearchSortMode\r\n */\r\n\r\nclass SearchSort {\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic Recent = '';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic Popular = 'popular';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic PopularMonth = 'popular-month';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic PopularWeek = 'popular-week';\r\n\t/**\r\n\t * @type {SearchSortMode}\r\n\t */\r\n\tstatic PopularToday = 'poplar-today';\r\n}\r\n\r\n/**\r\n * Class representing search request results.\r\n * @class\r\n */\r\nclass Search {\r\n\t/**\r\n\t * Parse search object into class instance.\r\n\t * @param {APISearch} search Search object.\r\n\t */\r\n\tstatic parse(search) {\r\n\t\treturn new this({\r\n\t\t\tpages: search.num_pages\r\n\t\t\t\t? +search.num_pages\r\n\t\t\t\t: 1,\r\n\t\t\tperPage: search.per_page\r\n\t\t\t\t? +search.per_page\r\n\t\t\t\t: search.result.length,\r\n\t\t\tbooks: search.result.map(Book.parse.bind(Book)),\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * API instance.\r\n\t * @type {?API}\r\n\t * @default null\r\n\t */\r\n\tapi = null;\r\n\r\n\t/**\r\n\t * Query string.\r\n\t * @type {?string}\r\n\t * @default null\r\n\t */\r\n\tquery = null;\r\n\r\n\t/**\r\n\t * Search sort mode.\r\n\t * @type {SearchSortMode}\r\n\t * @default ''\r\n\t */\r\n\tsort = '';\r\n\r\n\t/**\r\n\t * Page ID.\r\n\t * @type {number}\r\n\t * @default 1\r\n\t */\r\n\tpage = 1;\r\n\r\n\t/**\r\n\t * Books per page.\r\n\t * @type {number}\r\n\t * @default 0\r\n\t */\r\n\tperPage = 0;\r\n\r\n\t/**\r\n\t * Books array.\r\n\t * @type {Book[]}\r\n\t * @default []\r\n\t */\r\n\tbooks = [];\r\n\r\n\t/**\r\n\t * Pages count.\r\n\t * @type {number}\r\n\t * @default 1\r\n\t */\r\n\tpages = 1;\r\n\r\n\t/**\r\n\t * Create search.\r\n\t * @param {?object}         [params]           Search parameters.\r\n\t * @param {?string}         [params.query='']  Query string.\r\n\t * @param {?SearchSortMode} [params.sort='']   Search sort mode.\r\n\t * @param {?number}         [params.page=1]    Search page ID.\r\n\t * @param {?number}         [params.pages=1]   Search pages count.\r\n\t * @param {?number}         [params.perPage=0] Search books per page.\r\n\t * @param {?Book[]}         [params.books=[]]  Books array.\r\n\t */\r\n\tconstructor({\r\n\t\tquery   = null,\r\n\t\tsort    = '',\r\n\t\tpage    = 1,\r\n\t\tpages   = 1,\r\n\t\tperPage = 0,\r\n\t\tbooks   = [],\r\n\t} = {}) {\r\n\t\tif (Array.isArray(books))\r\n\t\t\tbooks.forEach(this.pushBook.bind(this));\r\n\r\n\t\tObject.assign(this, {\r\n\t\t\tquery,\r\n\t\t\tsort,\r\n\t\t\tpage,\r\n\t\t\tpages,\r\n\t\t\tperPage,\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * Push book to books array.\r\n\t * @param {Book} book Book.\r\n\t * @returns {boolean} Whatever was book added or not.\r\n\t * @private\r\n\t */\r\n\tpushBook(book) {\r\n\t\tif (book instanceof Book) {\r\n\t\t\tthis.books.push(book);\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t/**\r\n\t * Request next page.\r\n\t * @throws Error if search request can't be paginated.\r\n\t * @throws Error if `api` is missing as instance property or function argument.\r\n\t * @param {API} [api=this.api] API instance.\r\n\t * @returns {Promise<Search>} Next page search.\r\n\t */\r\n\tgetNextPage(api = this.api) {\r\n\t\tlet { query, page, sort, } = this;\r\n\t\tif (query === null)\r\n\t\t\tthrow Error('pagination impossible.');\r\n\t\tif (!(api instanceof API))\r\n\t\t\tthrow Error('api must exists.');\r\n\t\treturn query instanceof Tag\r\n\t\t\t? api.searchTagged(query, page + 1, sort)\r\n\t\t\t: api.search(query, page + 1, sort);\r\n\t}\r\n}\r\n\r\nexport {\r\n\tSearch,\r\n\tSearchSort,\r\n};\r\n", "var debug;\n\nmodule.exports = function () {\n  if (!debug) {\n    try {\n      /* eslint global-require: off */\n      debug = require(\"debug\")(\"follow-redirects\");\n    }\n    catch (error) { /* */ }\n    if (typeof debug !== \"function\") {\n      debug = function () { /* */ };\n    }\n  }\n  debug.apply(null, arguments);\n};\n", "var url = require(\"url\");\nvar URL = url.URL;\nvar http = require(\"http\");\nvar https = require(\"https\");\nvar Writable = require(\"stream\").Writable;\nvar assert = require(\"assert\");\nvar debug = require(\"./debug\");\n\n// Preventive platform detection\n// istanbul ignore next\n(function detectUnsupportedEnvironment() {\n  var looksLikeNode = typeof process !== \"undefined\";\n  var looksLikeBrowser = typeof window !== \"undefined\" && typeof document !== \"undefined\";\n  var looksLikeV8 = isFunction(Error.captureStackTrace);\n  if (!looksLikeNode && (looksLikeBrowser || !looksLikeV8)) {\n    console.warn(\"The follow-redirects package should be excluded from browser builds.\");\n  }\n}());\n\n// Whether to use the native URL object or the legacy url module\nvar useNativeURL = false;\ntry {\n  assert(new URL(\"\"));\n}\ncatch (error) {\n  useNativeURL = error.code === \"ERR_INVALID_URL\";\n}\n\n// URL fields to preserve in copy operations\nvar preservedUrlFields = [\n  \"auth\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"path\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"query\",\n  \"search\",\n  \"hash\",\n];\n\n// Create handlers that pass events from native requests\nvar events = [\"abort\", \"aborted\", \"connect\", \"error\", \"socket\", \"timeout\"];\nvar eventHandlers = Object.create(null);\nevents.forEach(function (event) {\n  eventHandlers[event] = function (arg1, arg2, arg3) {\n    this._redirectable.emit(event, arg1, arg2, arg3);\n  };\n});\n\n// Error types with codes\nvar InvalidUrlError = createErrorType(\n  \"ERR_INVALID_URL\",\n  \"Invalid URL\",\n  TypeError\n);\nvar RedirectionError = createErrorType(\n  \"ERR_FR_REDIRECTION_FAILURE\",\n  \"Redirected request failed\"\n);\nvar TooManyRedirectsError = createErrorType(\n  \"ERR_FR_TOO_MANY_REDIRECTS\",\n  \"Maximum number of redirects exceeded\",\n  RedirectionError\n);\nvar MaxBodyLengthExceededError = createErrorType(\n  \"ERR_FR_MAX_BODY_LENGTH_EXCEEDED\",\n  \"Request body larger than maxBodyLength limit\"\n);\nvar WriteAfterEndError = createErrorType(\n  \"ERR_STREAM_WRITE_AFTER_END\",\n  \"write after end\"\n);\n\n// istanbul ignore next\nvar destroy = Writable.prototype.destroy || noop;\n\n// An HTTP(S) request that can be redirected\nfunction RedirectableRequest(options, responseCallback) {\n  // Initialize the request\n  Writable.call(this);\n  this._sanitizeOptions(options);\n  this._options = options;\n  this._ended = false;\n  this._ending = false;\n  this._redirectCount = 0;\n  this._redirects = [];\n  this._requestBodyLength = 0;\n  this._requestBodyBuffers = [];\n\n  // Attach a callback if passed\n  if (responseCallback) {\n    this.on(\"response\", responseCallback);\n  }\n\n  // React to responses of native requests\n  var self = this;\n  this._onNativeResponse = function (response) {\n    try {\n      self._processResponse(response);\n    }\n    catch (cause) {\n      self.emit(\"error\", cause instanceof RedirectionError ?\n        cause : new RedirectionError({ cause: cause }));\n    }\n  };\n\n  // Perform the first request\n  this._performRequest();\n}\nRedirectableRequest.prototype = Object.create(Writable.prototype);\n\nRedirectableRequest.prototype.abort = function () {\n  destroyRequest(this._currentRequest);\n  this._currentRequest.abort();\n  this.emit(\"abort\");\n};\n\nRedirectableRequest.prototype.destroy = function (error) {\n  destroyRequest(this._currentRequest, error);\n  destroy.call(this, error);\n  return this;\n};\n\n// Writes buffered data to the current native request\nRedirectableRequest.prototype.write = function (data, encoding, callback) {\n  // Writing is not allowed if end has been called\n  if (this._ending) {\n    throw new WriteAfterEndError();\n  }\n\n  // Validate input and shift parameters if necessary\n  if (!isString(data) && !isBuffer(data)) {\n    throw new TypeError(\"data should be a string, Buffer or Uint8Array\");\n  }\n  if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Ignore empty buffers, since writing them doesn't invoke the callback\n  // https://github.com/nodejs/node/issues/22066\n  if (data.length === 0) {\n    if (callback) {\n      callback();\n    }\n    return;\n  }\n  // Only write when we don't exceed the maximum body length\n  if (this._requestBodyLength + data.length <= this._options.maxBodyLength) {\n    this._requestBodyLength += data.length;\n    this._requestBodyBuffers.push({ data: data, encoding: encoding });\n    this._currentRequest.write(data, encoding, callback);\n  }\n  // Error when we exceed the maximum body length\n  else {\n    this.emit(\"error\", new MaxBodyLengthExceededError());\n    this.abort();\n  }\n};\n\n// Ends the current native request\nRedirectableRequest.prototype.end = function (data, encoding, callback) {\n  // Shift parameters if necessary\n  if (isFunction(data)) {\n    callback = data;\n    data = encoding = null;\n  }\n  else if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Write data if needed and end\n  if (!data) {\n    this._ended = this._ending = true;\n    this._currentRequest.end(null, null, callback);\n  }\n  else {\n    var self = this;\n    var currentRequest = this._currentRequest;\n    this.write(data, encoding, function () {\n      self._ended = true;\n      currentRequest.end(null, null, callback);\n    });\n    this._ending = true;\n  }\n};\n\n// Sets a header value on the current native request\nRedirectableRequest.prototype.setHeader = function (name, value) {\n  this._options.headers[name] = value;\n  this._currentRequest.setHeader(name, value);\n};\n\n// Clears a header value on the current native request\nRedirectableRequest.prototype.removeHeader = function (name) {\n  delete this._options.headers[name];\n  this._currentRequest.removeHeader(name);\n};\n\n// Global timeout for all underlying requests\nRedirectableRequest.prototype.setTimeout = function (msecs, callback) {\n  var self = this;\n\n  // Destroys the socket on timeout\n  function destroyOnTimeout(socket) {\n    socket.setTimeout(msecs);\n    socket.removeListener(\"timeout\", socket.destroy);\n    socket.addListener(\"timeout\", socket.destroy);\n  }\n\n  // Sets up a timer to trigger a timeout event\n  function startTimer(socket) {\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n    }\n    self._timeout = setTimeout(function () {\n      self.emit(\"timeout\");\n      clearTimer();\n    }, msecs);\n    destroyOnTimeout(socket);\n  }\n\n  // Stops a timeout from triggering\n  function clearTimer() {\n    // Clear the timeout\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n      self._timeout = null;\n    }\n\n    // Clean up all attached listeners\n    self.removeListener(\"abort\", clearTimer);\n    self.removeListener(\"error\", clearTimer);\n    self.removeListener(\"response\", clearTimer);\n    self.removeListener(\"close\", clearTimer);\n    if (callback) {\n      self.removeListener(\"timeout\", callback);\n    }\n    if (!self.socket) {\n      self._currentRequest.removeListener(\"socket\", startTimer);\n    }\n  }\n\n  // Attach callback if passed\n  if (callback) {\n    this.on(\"timeout\", callback);\n  }\n\n  // Start the timer if or when the socket is opened\n  if (this.socket) {\n    startTimer(this.socket);\n  }\n  else {\n    this._currentRequest.once(\"socket\", startTimer);\n  }\n\n  // Clean up on events\n  this.on(\"socket\", destroyOnTimeout);\n  this.on(\"abort\", clearTimer);\n  this.on(\"error\", clearTimer);\n  this.on(\"response\", clearTimer);\n  this.on(\"close\", clearTimer);\n\n  return this;\n};\n\n// Proxy all other public ClientRequest methods\n[\n  \"flushHeaders\", \"getHeader\",\n  \"setNoDelay\", \"setSocketKeepAlive\",\n].forEach(function (method) {\n  RedirectableRequest.prototype[method] = function (a, b) {\n    return this._currentRequest[method](a, b);\n  };\n});\n\n// Proxy all public ClientRequest properties\n[\"aborted\", \"connection\", \"socket\"].forEach(function (property) {\n  Object.defineProperty(RedirectableRequest.prototype, property, {\n    get: function () { return this._currentRequest[property]; },\n  });\n});\n\nRedirectableRequest.prototype._sanitizeOptions = function (options) {\n  // Ensure headers are always present\n  if (!options.headers) {\n    options.headers = {};\n  }\n\n  // Since http.request treats host as an alias of hostname,\n  // but the url module interprets host as hostname plus port,\n  // eliminate the host property to avoid confusion.\n  if (options.host) {\n    // Use hostname if set, because it has precedence\n    if (!options.hostname) {\n      options.hostname = options.host;\n    }\n    delete options.host;\n  }\n\n  // Complete the URL object when necessary\n  if (!options.pathname && options.path) {\n    var searchPos = options.path.indexOf(\"?\");\n    if (searchPos < 0) {\n      options.pathname = options.path;\n    }\n    else {\n      options.pathname = options.path.substring(0, searchPos);\n      options.search = options.path.substring(searchPos);\n    }\n  }\n};\n\n\n// Executes the next native request (initial or redirect)\nRedirectableRequest.prototype._performRequest = function () {\n  // Load the native protocol\n  var protocol = this._options.protocol;\n  var nativeProtocol = this._options.nativeProtocols[protocol];\n  if (!nativeProtocol) {\n    throw new TypeError(\"Unsupported protocol \" + protocol);\n  }\n\n  // If specified, use the agent corresponding to the protocol\n  // (HTTP and HTTPS use different types of agents)\n  if (this._options.agents) {\n    var scheme = protocol.slice(0, -1);\n    this._options.agent = this._options.agents[scheme];\n  }\n\n  // Create the native request and set up its event handlers\n  var request = this._currentRequest =\n        nativeProtocol.request(this._options, this._onNativeResponse);\n  request._redirectable = this;\n  for (var event of events) {\n    request.on(event, eventHandlers[event]);\n  }\n\n  // RFC7230§5.3.1: When making a request directly to an origin server, […]\n  // a client MUST send only the absolute path […] as the request-target.\n  this._currentUrl = /^\\//.test(this._options.path) ?\n    url.format(this._options) :\n    // When making a request to a proxy, […]\n    // a client MUST send the target URI in absolute-form […].\n    this._options.path;\n\n  // End a redirected request\n  // (The first request must be ended explicitly with RedirectableRequest#end)\n  if (this._isRedirect) {\n    // Write the request entity and end\n    var i = 0;\n    var self = this;\n    var buffers = this._requestBodyBuffers;\n    (function writeNext(error) {\n      // Only write if this request has not been redirected yet\n      // istanbul ignore else\n      if (request === self._currentRequest) {\n        // Report any write errors\n        // istanbul ignore if\n        if (error) {\n          self.emit(\"error\", error);\n        }\n        // Write the next buffer if there are still left\n        else if (i < buffers.length) {\n          var buffer = buffers[i++];\n          // istanbul ignore else\n          if (!request.finished) {\n            request.write(buffer.data, buffer.encoding, writeNext);\n          }\n        }\n        // End the request if `end` has been called on us\n        else if (self._ended) {\n          request.end();\n        }\n      }\n    }());\n  }\n};\n\n// Processes a response from the current native request\nRedirectableRequest.prototype._processResponse = function (response) {\n  // Store the redirected response\n  var statusCode = response.statusCode;\n  if (this._options.trackRedirects) {\n    this._redirects.push({\n      url: this._currentUrl,\n      headers: response.headers,\n      statusCode: statusCode,\n    });\n  }\n\n  // RFC7231§6.4: The 3xx (Redirection) class of status code indicates\n  // that further action needs to be taken by the user agent in order to\n  // fulfill the request. If a Location header field is provided,\n  // the user agent MAY automatically redirect its request to the URI\n  // referenced by the Location field value,\n  // even if the specific status code is not understood.\n\n  // If the response is not a redirect; return it as-is\n  var location = response.headers.location;\n  if (!location || this._options.followRedirects === false ||\n      statusCode < 300 || statusCode >= 400) {\n    response.responseUrl = this._currentUrl;\n    response.redirects = this._redirects;\n    this.emit(\"response\", response);\n\n    // Clean up\n    this._requestBodyBuffers = [];\n    return;\n  }\n\n  // The response is a redirect, so abort the current request\n  destroyRequest(this._currentRequest);\n  // Discard the remainder of the response to avoid waiting for data\n  response.destroy();\n\n  // RFC7231§6.4: A client SHOULD detect and intervene\n  // in cyclical redirections (i.e., \"infinite\" redirection loops).\n  if (++this._redirectCount > this._options.maxRedirects) {\n    throw new TooManyRedirectsError();\n  }\n\n  // Store the request headers if applicable\n  var requestHeaders;\n  var beforeRedirect = this._options.beforeRedirect;\n  if (beforeRedirect) {\n    requestHeaders = Object.assign({\n      // The Host header was set by nativeProtocol.request\n      Host: response.req.getHeader(\"host\"),\n    }, this._options.headers);\n  }\n\n  // RFC7231§6.4: Automatic redirection needs to done with\n  // care for methods not known to be safe, […]\n  // RFC7231§6.4.2–3: For historical reasons, a user agent MAY change\n  // the request method from POST to GET for the subsequent request.\n  var method = this._options.method;\n  if ((statusCode === 301 || statusCode === 302) && this._options.method === \"POST\" ||\n      // RFC7231§6.4.4: The 303 (See Other) status code indicates that\n      // the server is redirecting the user agent to a different resource […]\n      // A user agent can perform a retrieval request targeting that URI\n      // (a GET or HEAD request if using HTTP) […]\n      (statusCode === 303) && !/^(?:GET|HEAD)$/.test(this._options.method)) {\n    this._options.method = \"GET\";\n    // Drop a possible entity and headers related to it\n    this._requestBodyBuffers = [];\n    removeMatchingHeaders(/^content-/i, this._options.headers);\n  }\n\n  // Drop the Host header, as the redirect might lead to a different host\n  var currentHostHeader = removeMatchingHeaders(/^host$/i, this._options.headers);\n\n  // If the redirect is relative, carry over the host of the last request\n  var currentUrlParts = parseUrl(this._currentUrl);\n  var currentHost = currentHostHeader || currentUrlParts.host;\n  var currentUrl = /^\\w+:/.test(location) ? this._currentUrl :\n    url.format(Object.assign(currentUrlParts, { host: currentHost }));\n\n  // Create the redirected request\n  var redirectUrl = resolveUrl(location, currentUrl);\n  debug(\"redirecting to\", redirectUrl.href);\n  this._isRedirect = true;\n  spreadUrlObject(redirectUrl, this._options);\n\n  // Drop confidential headers when redirecting to a less secure protocol\n  // or to a different domain that is not a superdomain\n  if (redirectUrl.protocol !== currentUrlParts.protocol &&\n     redirectUrl.protocol !== \"https:\" ||\n     redirectUrl.host !== currentHost &&\n     !isSubdomain(redirectUrl.host, currentHost)) {\n    removeMatchingHeaders(/^(?:(?:proxy-)?authorization|cookie)$/i, this._options.headers);\n  }\n\n  // Evaluate the beforeRedirect callback\n  if (isFunction(beforeRedirect)) {\n    var responseDetails = {\n      headers: response.headers,\n      statusCode: statusCode,\n    };\n    var requestDetails = {\n      url: currentUrl,\n      method: method,\n      headers: requestHeaders,\n    };\n    beforeRedirect(this._options, responseDetails, requestDetails);\n    this._sanitizeOptions(this._options);\n  }\n\n  // Perform the redirected request\n  this._performRequest();\n};\n\n// Wraps the key/value object of protocols with redirect functionality\nfunction wrap(protocols) {\n  // Default settings\n  var exports = {\n    maxRedirects: 21,\n    maxBodyLength: 10 * 1024 * 1024,\n  };\n\n  // Wrap each protocol\n  var nativeProtocols = {};\n  Object.keys(protocols).forEach(function (scheme) {\n    var protocol = scheme + \":\";\n    var nativeProtocol = nativeProtocols[protocol] = protocols[scheme];\n    var wrappedProtocol = exports[scheme] = Object.create(nativeProtocol);\n\n    // Executes a request, following redirects\n    function request(input, options, callback) {\n      // Parse parameters, ensuring that input is an object\n      if (isURL(input)) {\n        input = spreadUrlObject(input);\n      }\n      else if (isString(input)) {\n        input = spreadUrlObject(parseUrl(input));\n      }\n      else {\n        callback = options;\n        options = validateUrl(input);\n        input = { protocol: protocol };\n      }\n      if (isFunction(options)) {\n        callback = options;\n        options = null;\n      }\n\n      // Set defaults\n      options = Object.assign({\n        maxRedirects: exports.maxRedirects,\n        maxBodyLength: exports.maxBodyLength,\n      }, input, options);\n      options.nativeProtocols = nativeProtocols;\n      if (!isString(options.host) && !isString(options.hostname)) {\n        options.hostname = \"::1\";\n      }\n\n      assert.equal(options.protocol, protocol, \"protocol mismatch\");\n      debug(\"options\", options);\n      return new RedirectableRequest(options, callback);\n    }\n\n    // Executes a GET request, following redirects\n    function get(input, options, callback) {\n      var wrappedRequest = wrappedProtocol.request(input, options, callback);\n      wrappedRequest.end();\n      return wrappedRequest;\n    }\n\n    // Expose the properties on the wrapped protocol\n    Object.defineProperties(wrappedProtocol, {\n      request: { value: request, configurable: true, enumerable: true, writable: true },\n      get: { value: get, configurable: true, enumerable: true, writable: true },\n    });\n  });\n  return exports;\n}\n\nfunction noop() { /* empty */ }\n\nfunction parseUrl(input) {\n  var parsed;\n  // istanbul ignore else\n  if (useNativeURL) {\n    parsed = new URL(input);\n  }\n  else {\n    // Ensure the URL is valid and absolute\n    parsed = validateUrl(url.parse(input));\n    if (!isString(parsed.protocol)) {\n      throw new InvalidUrlError({ input });\n    }\n  }\n  return parsed;\n}\n\nfunction resolveUrl(relative, base) {\n  // istanbul ignore next\n  return useNativeURL ? new URL(relative, base) : parseUrl(url.resolve(base, relative));\n}\n\nfunction validateUrl(input) {\n  if (/^\\[/.test(input.hostname) && !/^\\[[:0-9a-f]+\\]$/i.test(input.hostname)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  if (/^\\[/.test(input.host) && !/^\\[[:0-9a-f]+\\](:\\d+)?$/i.test(input.host)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  return input;\n}\n\nfunction spreadUrlObject(urlObject, target) {\n  var spread = target || {};\n  for (var key of preservedUrlFields) {\n    spread[key] = urlObject[key];\n  }\n\n  // Fix IPv6 hostname\n  if (spread.hostname.startsWith(\"[\")) {\n    spread.hostname = spread.hostname.slice(1, -1);\n  }\n  // Ensure port is a number\n  if (spread.port !== \"\") {\n    spread.port = Number(spread.port);\n  }\n  // Concatenate path\n  spread.path = spread.search ? spread.pathname + spread.search : spread.pathname;\n\n  return spread;\n}\n\nfunction removeMatchingHeaders(regex, headers) {\n  var lastValue;\n  for (var header in headers) {\n    if (regex.test(header)) {\n      lastValue = headers[header];\n      delete headers[header];\n    }\n  }\n  return (lastValue === null || typeof lastValue === \"undefined\") ?\n    undefined : String(lastValue).trim();\n}\n\nfunction createErrorType(code, message, baseClass) {\n  // Create constructor\n  function CustomError(properties) {\n    // istanbul ignore else\n    if (isFunction(Error.captureStackTrace)) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n    Object.assign(this, properties || {});\n    this.code = code;\n    this.message = this.cause ? message + \": \" + this.cause.message : message;\n  }\n\n  // Attach constructor and set default properties\n  CustomError.prototype = new (baseClass || Error)();\n  Object.defineProperties(CustomError.prototype, {\n    constructor: {\n      value: CustomError,\n      enumerable: false,\n    },\n    name: {\n      value: \"Error [\" + code + \"]\",\n      enumerable: false,\n    },\n  });\n  return CustomError;\n}\n\nfunction destroyRequest(request, error) {\n  for (var event of events) {\n    request.removeListener(event, eventHandlers[event]);\n  }\n  request.on(\"error\", noop);\n  request.destroy(error);\n}\n\nfunction isSubdomain(subdomain, domain) {\n  assert(isString(subdomain) && isString(domain));\n  var dot = subdomain.length - domain.length - 1;\n  return dot > 0 && subdomain[dot] === \".\" && subdomain.endsWith(domain);\n}\n\nfunction isString(value) {\n  return typeof value === \"string\" || value instanceof String;\n}\n\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\nfunction isBuffer(value) {\n  return typeof value === \"object\" && (\"length\" in value);\n}\n\nfunction isURL(value) {\n  return URL && value instanceof URL;\n}\n\n// Exports\nmodule.exports = wrap({ http: http, https: https });\nmodule.exports.wrap = wrap;\n", "const FixedFIFO = require('./fixed-size')\n\nmodule.exports = class FastFIFO {\n  constructor (hwm) {\n    this.hwm = hwm || 16\n    this.head = new FixedFIFO(this.hwm)\n    this.tail = this.head\n    this.length = 0\n  }\n\n  clear () {\n    this.head = this.tail\n    this.head.clear()\n    this.length = 0\n  }\n\n  push (val) {\n    this.length++\n    if (!this.head.push(val)) {\n      const prev = this.head\n      this.head = prev.next = new FixedFIFO(2 * this.head.buffer.length)\n      this.head.push(val)\n    }\n  }\n\n  shift () {\n    if (this.length !== 0) this.length--\n    const val = this.tail.shift()\n    if (val === undefined && this.tail.next) {\n      const next = this.tail.next\n      this.tail.next = null\n      this.tail = next\n      return this.tail.shift()\n    }\n\n    return val\n  }\n\n  peek () {\n    const val = this.tail.peek()\n    if (val === undefined && this.tail.next) return this.tail.next.peek()\n    return val\n  }\n\n  isEmpty () {\n    return this.length === 0\n  }\n}\n", "module.exports = class FixedFIFO {\n  constructor (hwm) {\n    if (!(hwm > 0) || ((hwm - 1) & hwm) !== 0) throw new Error('Max size for a FixedFIFO should be a power of two')\n    this.buffer = new Array(hwm)\n    this.mask = hwm - 1\n    this.top = 0\n    this.btm = 0\n    this.next = null\n  }\n\n  clear () {\n    this.top = this.btm = 0\n    this.next = null\n    this.buffer.fill(undefined)\n  }\n\n  push (data) {\n    if (this.buffer[this.top] !== undefined) return false\n    this.buffer[this.top] = data\n    this.top = (this.top + 1) & this.mask\n    return true\n  }\n\n  shift () {\n    const last = this.buffer[this.btm]\n    if (last === undefined) return undefined\n    this.buffer[this.btm] = undefined\n    this.btm = (this.btm + 1) & this.mask\n    return last\n  }\n\n  peek () {\n    return this.buffer[this.btm]\n  }\n\n  isEmpty () {\n    return this.buffer[this.btm] === undefined\n  }\n}\n", "function isBuffer (value) {\n  return Buffer.isBuffer(value) || value instanceof Uint8Array\n}\n\nfunction isEncoding (encoding) {\n  return Buffer.isEncoding(encoding)\n}\n\nfunction alloc (size, fill, encoding) {\n  return Buffer.alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  return Buffer.allocUnsafe(size)\n}\n\nfunction allocUnsafeSlow (size) {\n  return Buffer.allocUnsafeSlow(size)\n}\n\nfunction byteLength (string, encoding) {\n  return Buffer.byteLength(string, encoding)\n}\n\nfunction compare (a, b) {\n  return Buffer.compare(a, b)\n}\n\nfunction concat (buffers, totalLength) {\n  return Buffer.concat(buffers, totalLength)\n}\n\nfunction copy (source, target, targetStart, start, end) {\n  return toBuffer(source).copy(target, targetStart, start, end)\n}\n\nfunction equals (a, b) {\n  return toBuffer(a).equals(b)\n}\n\nfunction fill (buffer, value, offset, end, encoding) {\n  return toBuffer(buffer).fill(value, offset, end, encoding)\n}\n\nfunction from (value, encodingOrOffset, length) {\n  return Buffer.from(value, encodingOrOffset, length)\n}\n\nfunction includes (buffer, value, byteOffset, encoding) {\n  return toBuffer(buffer).includes(value, byteOffset, encoding)\n}\n\nfunction indexOf (buffer, value, byfeOffset, encoding) {\n  return toBuffer(buffer).indexOf(value, byfeOffset, encoding)\n}\n\nfunction lastIndexOf (buffer, value, byteOffset, encoding) {\n  return toBuffer(buffer).lastIndexOf(value, byteOffset, encoding)\n}\n\nfunction swap16 (buffer) {\n  return toBuffer(buffer).swap16()\n}\n\nfunction swap32 (buffer) {\n  return toBuffer(buffer).swap32()\n}\n\nfunction swap64 (buffer) {\n  return toBuffer(buffer).swap64()\n}\n\nfunction toBuffer (buffer) {\n  if (Buffer.isBuffer(buffer)) return buffer\n  return Buffer.from(buffer.buffer, buffer.byteOffset, buffer.byteLength)\n}\n\nfunction toString (buffer, encoding, start, end) {\n  return toBuffer(buffer).toString(encoding, start, end)\n}\n\nfunction write (buffer, string, offset, length, encoding) {\n  return toBuffer(buffer).write(string, offset, length, encoding)\n}\n\nfunction writeDoubleLE (buffer, value, offset) {\n  return toBuffer(buffer).writeDoubleLE(value, offset)\n}\n\nfunction writeFloatLE (buffer, value, offset) {\n  return toBuffer(buffer).writeFloatLE(value, offset)\n}\n\nfunction writeUInt32LE (buffer, value, offset) {\n  return toBuffer(buffer).writeUInt32LE(value, offset)\n}\n\nfunction writeInt32LE (buffer, value, offset) {\n  return toBuffer(buffer).writeInt32LE(value, offset)\n}\n\nfunction readDoubleLE (buffer, offset) {\n  return toBuffer(buffer).readDoubleLE(offset)\n}\n\nfunction readFloatLE (buffer, offset) {\n  return toBuffer(buffer).readFloatLE(offset)\n}\n\nfunction readUInt32LE (buffer, offset) {\n  return toBuffer(buffer).readUInt32LE(offset)\n}\n\nfunction readInt32LE (buffer, offset) {\n  return toBuffer(buffer).readInt32LE(offset)\n}\n\nfunction writeDoubleBE (buffer, value, offset) {\n  return toBuffer(buffer).writeDoubleBE(value, offset)\n}\n\nfunction writeFloatBE (buffer, value, offset) {\n  return toBuffer(buffer).writeFloatBE(value, offset)\n}\n\nfunction writeUInt32BE (buffer, value, offset) {\n  return toBuffer(buffer).writeUInt32BE(value, offset)\n}\n\nfunction writeInt32BE (buffer, value, offset) {\n  return toBuffer(buffer).writeInt32BE(value, offset)\n}\n\nfunction readDoubleBE (buffer, offset) {\n  return toBuffer(buffer).readDoubleBE(offset)\n}\n\nfunction readFloatBE (buffer, offset) {\n  return toBuffer(buffer).readFloatBE(offset)\n}\n\nfunction readUInt32BE (buffer, offset) {\n  return toBuffer(buffer).readUInt32BE(offset)\n}\n\nfunction readInt32BE (buffer, offset) {\n  return toBuffer(buffer).readInt32BE(offset)\n}\n\nmodule.exports = {\n  isBuffer,\n  isEncoding,\n  alloc,\n  allocUnsafe,\n  allocUnsafeSlow,\n  byteLength,\n  compare,\n  concat,\n  copy,\n  equals,\n  fill,\n  from,\n  includes,\n  indexOf,\n  lastIndexOf,\n  swap16,\n  swap32,\n  swap64,\n  toBuffer,\n  toString,\n  write,\n  writeDoubleLE,\n  writeFloatLE,\n  writeUInt32LE,\n  writeInt32LE,\n  readDoubleLE,\n  readFloatLE,\n  readUInt32LE,\n  readInt32LE,\n  writeDoubleBE,\n  writeFloatBE,\n  writeUInt32BE,\n  writeInt32BE,\n  readDoubleBE,\n  readFloatBE,\n  readUInt32BE,\n  readInt32BE\n\n}\n", "const b4a = require('b4a')\n\nmodule.exports = class PassThroughDecoder {\n  constructor (encoding) {\n    this.encoding = encoding\n  }\n\n  get remaining () {\n    return 0\n  }\n\n  decode (tail) {\n    return b4a.toString(tail, this.encoding)\n  }\n\n  flush () {\n    return ''\n  }\n}\n", "const b4a = require('b4a')\n\n/**\n * https://encoding.spec.whatwg.org/#utf-8-decoder\n */\nmodule.exports = class UTF8Decoder {\n  constructor () {\n    this.codePoint = 0\n    this.bytesSeen = 0\n    this.bytesNeeded = 0\n    this.lowerBoundary = 0x80\n    this.upperBoundary = 0xbf\n  }\n\n  get remaining () {\n    return this.bytesSeen\n  }\n\n  decode (data) {\n    // If we have a fast path, just sniff if the last part is a boundary\n    if (this.bytesNeeded === 0) {\n      let isBoundary = true\n\n      for (let i = Math.max(0, data.byteLength - 4), n = data.byteLength; i < n && isBoundary; i++) {\n        isBoundary = data[i] <= 0x7f\n      }\n\n      if (isBoundary) return b4a.toString(data, 'utf8')\n    }\n\n    let result = ''\n\n    for (let i = 0, n = data.byteLength; i < n; i++) {\n      const byte = data[i]\n\n      if (this.bytesNeeded === 0) {\n        if (byte <= 0x7f) {\n          result += String.fromCharCode(byte)\n        } else {\n          this.bytesSeen = 1\n\n          if (byte >= 0xc2 && byte <= 0xdf) {\n            this.bytesNeeded = 2\n            this.codePoint = byte & 0x1f\n          } else if (byte >= 0xe0 && byte <= 0xef) {\n            if (byte === 0xe0) this.lowerBoundary = 0xa0\n            else if (byte === 0xed) this.upperBoundary = 0x9f\n            this.bytesNeeded = 3\n            this.codePoint = byte & 0xf\n          } else if (byte >= 0xf0 && byte <= 0xf4) {\n            if (byte === 0xf0) this.lowerBoundary = 0x90\n            if (byte === 0xf4) this.upperBoundary = 0x8f\n            this.bytesNeeded = 4\n            this.codePoint = byte & 0x7\n          } else {\n            result += '\\ufffd'\n          }\n        }\n\n        continue\n      }\n\n      if (byte < this.lowerBoundary || byte > this.upperBoundary) {\n        this.codePoint = 0\n        this.bytesNeeded = 0\n        this.bytesSeen = 0\n        this.lowerBoundary = 0x80\n        this.upperBoundary = 0xbf\n\n        result += '\\ufffd'\n\n        continue\n      }\n\n      this.lowerBoundary = 0x80\n      this.upperBoundary = 0xbf\n\n      this.codePoint = (this.codePoint << 6) | (byte & 0x3f)\n      this.bytesSeen++\n\n      if (this.bytesSeen !== this.bytesNeeded) continue\n\n      result += String.fromCodePoint(this.codePoint)\n\n      this.codePoint = 0\n      this.bytesNeeded = 0\n      this.bytesSeen = 0\n    }\n\n    return result\n  }\n\n  flush () {\n    const result = this.bytesNeeded > 0 ? '\\ufffd' : ''\n\n    this.codePoint = 0\n    this.bytesNeeded = 0\n    this.bytesSeen = 0\n    this.lowerBoundary = 0x80\n    this.upperBoundary = 0xbf\n\n    return result\n  }\n}\n", "const PassThroughDecoder = require('./lib/pass-through-decoder')\nconst UTF8Decoder = require('./lib/utf8-decoder')\n\nmodule.exports = class TextDecoder {\n  constructor (encoding = 'utf8') {\n    this.encoding = normalizeEncoding(encoding)\n\n    switch (this.encoding) {\n      case 'utf8':\n        this.decoder = new UTF8Decoder()\n        break\n      case 'utf16le':\n      case 'base64':\n        throw new Error('Unsupported encoding: ' + this.encoding)\n      default:\n        this.decoder = new PassThroughDecoder(this.encoding)\n    }\n  }\n\n  get remaining () {\n    return this.decoder.remaining\n  }\n\n  push (data) {\n    if (typeof data === 'string') return data\n    return this.decoder.decode(data)\n  }\n\n  // For Node.js compatibility\n  write (data) {\n    return this.push(data)\n  }\n\n  end (data) {\n    let result = ''\n    if (data) result = this.push(data)\n    result += this.decoder.flush()\n    return result\n  }\n}\n\nfunction normalizeEncoding (encoding) {\n  encoding = encoding.toLowerCase()\n\n  switch (encoding) {\n    case 'utf8':\n    case 'utf-8':\n      return 'utf8'\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return 'utf16le'\n    case 'latin1':\n    case 'binary':\n      return 'latin1'\n    case 'base64':\n    case 'ascii':\n    case 'hex':\n      return encoding\n    default:\n      throw new Error('Unknown encoding: ' + encoding)\n  }\n};\n", "const { EventEmitter } = require('events')\nconst STREAM_DESTROYED = new Error('Stream was destroyed')\nconst PREMATURE_CLOSE = new Error('Premature close')\n\nconst FIFO = require('fast-fifo')\nconst TextDecoder = require('text-decoder')\n\n// if we do a future major, expect queue microtask to be there always, for now a bit defensive\nconst qmt = typeof queueMicrotask === 'undefined' ? fn => global.process.nextTick(fn) : queueMicrotask\n\n/* eslint-disable no-multi-spaces */\n\n// 29 bits used total (4 from shared, 14 from read, and 11 from write)\nconst MAX = ((1 << 29) - 1)\n\n// Shared state\nconst OPENING       = 0b0001\nconst PREDESTROYING = 0b0010\nconst DESTROYING    = 0b0100\nconst DESTROYED     = 0b1000\n\nconst NOT_OPENING = MAX ^ OPENING\nconst NOT_PREDESTROYING = MAX ^ PREDESTROYING\n\n// Read state (4 bit offset from shared state)\nconst READ_ACTIVE           = 0b00000000000001 << 4\nconst READ_UPDATING         = 0b00000000000010 << 4\nconst READ_PRIMARY          = 0b00000000000100 << 4\nconst READ_QUEUED           = 0b00000000001000 << 4\nconst READ_RESUMED          = 0b00000000010000 << 4\nconst READ_PIPE_DRAINED     = 0b00000000100000 << 4\nconst READ_ENDING           = 0b00000001000000 << 4\nconst READ_EMIT_DATA        = 0b00000010000000 << 4\nconst READ_EMIT_READABLE    = 0b00000100000000 << 4\nconst READ_EMITTED_READABLE = 0b00001000000000 << 4\nconst READ_DONE             = 0b00010000000000 << 4\nconst READ_NEXT_TICK        = 0b00100000000000 << 4\nconst READ_NEEDS_PUSH       = 0b01000000000000 << 4\nconst READ_READ_AHEAD       = 0b10000000000000 << 4\n\n// Combined read state\nconst READ_FLOWING = READ_RESUMED | READ_PIPE_DRAINED\nconst READ_ACTIVE_AND_NEEDS_PUSH = READ_ACTIVE | READ_NEEDS_PUSH\nconst READ_PRIMARY_AND_ACTIVE = READ_PRIMARY | READ_ACTIVE\nconst READ_EMIT_READABLE_AND_QUEUED = READ_EMIT_READABLE | READ_QUEUED\nconst READ_RESUMED_READ_AHEAD = READ_RESUMED | READ_READ_AHEAD\n\nconst READ_NOT_ACTIVE             = MAX ^ READ_ACTIVE\nconst READ_NON_PRIMARY            = MAX ^ READ_PRIMARY\nconst READ_NON_PRIMARY_AND_PUSHED = MAX ^ (READ_PRIMARY | READ_NEEDS_PUSH)\nconst READ_PUSHED                 = MAX ^ READ_NEEDS_PUSH\nconst READ_PAUSED                 = MAX ^ READ_RESUMED\nconst READ_NOT_QUEUED             = MAX ^ (READ_QUEUED | READ_EMITTED_READABLE)\nconst READ_NOT_ENDING             = MAX ^ READ_ENDING\nconst READ_PIPE_NOT_DRAINED       = MAX ^ READ_FLOWING\nconst READ_NOT_NEXT_TICK          = MAX ^ READ_NEXT_TICK\nconst READ_NOT_UPDATING           = MAX ^ READ_UPDATING\nconst READ_NO_READ_AHEAD          = MAX ^ READ_READ_AHEAD\nconst READ_PAUSED_NO_READ_AHEAD   = MAX ^ READ_RESUMED_READ_AHEAD\n\n// Write state (18 bit offset, 4 bit offset from shared state and 14 from read state)\nconst WRITE_ACTIVE     = 0b00000000001 << 18\nconst WRITE_UPDATING   = 0b00000000010 << 18\nconst WRITE_PRIMARY    = 0b00000000100 << 18\nconst WRITE_QUEUED     = 0b00000001000 << 18\nconst WRITE_UNDRAINED  = 0b00000010000 << 18\nconst WRITE_DONE       = 0b00000100000 << 18\nconst WRITE_EMIT_DRAIN = 0b00001000000 << 18\nconst WRITE_NEXT_TICK  = 0b00010000000 << 18\nconst WRITE_WRITING    = 0b00100000000 << 18\nconst WRITE_FINISHING  = 0b01000000000 << 18\nconst WRITE_CORKED     = 0b10000000000 << 18\n\nconst WRITE_NOT_ACTIVE    = MAX ^ (WRITE_ACTIVE | WRITE_WRITING)\nconst WRITE_NON_PRIMARY   = MAX ^ WRITE_PRIMARY\nconst WRITE_NOT_FINISHING = MAX ^ (WRITE_ACTIVE | WRITE_FINISHING)\nconst WRITE_DRAINED       = MAX ^ WRITE_UNDRAINED\nconst WRITE_NOT_QUEUED    = MAX ^ WRITE_QUEUED\nconst WRITE_NOT_NEXT_TICK = MAX ^ WRITE_NEXT_TICK\nconst WRITE_NOT_UPDATING  = MAX ^ WRITE_UPDATING\nconst WRITE_NOT_CORKED    = MAX ^ WRITE_CORKED\n\n// Combined shared state\nconst ACTIVE = READ_ACTIVE | WRITE_ACTIVE\nconst NOT_ACTIVE = MAX ^ ACTIVE\nconst DONE = READ_DONE | WRITE_DONE\nconst DESTROY_STATUS = DESTROYING | DESTROYED | PREDESTROYING\nconst OPEN_STATUS = DESTROY_STATUS | OPENING\nconst AUTO_DESTROY = DESTROY_STATUS | DONE\nconst NON_PRIMARY = WRITE_NON_PRIMARY & READ_NON_PRIMARY\nconst ACTIVE_OR_TICKING = WRITE_NEXT_TICK | READ_NEXT_TICK\nconst TICKING = ACTIVE_OR_TICKING & NOT_ACTIVE\nconst IS_OPENING = OPEN_STATUS | TICKING\n\n// Combined shared state and read state\nconst READ_PRIMARY_STATUS = OPEN_STATUS | READ_ENDING | READ_DONE\nconst READ_STATUS = OPEN_STATUS | READ_DONE | READ_QUEUED\nconst READ_ENDING_STATUS = OPEN_STATUS | READ_ENDING | READ_QUEUED\nconst READ_READABLE_STATUS = OPEN_STATUS | READ_EMIT_READABLE | READ_QUEUED | READ_EMITTED_READABLE\nconst SHOULD_NOT_READ = OPEN_STATUS | READ_ACTIVE | READ_ENDING | READ_DONE | READ_NEEDS_PUSH | READ_READ_AHEAD\nconst READ_BACKPRESSURE_STATUS = DESTROY_STATUS | READ_ENDING | READ_DONE\nconst READ_UPDATE_SYNC_STATUS = READ_UPDATING | OPEN_STATUS | READ_NEXT_TICK | READ_PRIMARY\nconst READ_NEXT_TICK_OR_OPENING = READ_NEXT_TICK | OPENING\n\n// Combined write state\nconst WRITE_PRIMARY_STATUS = OPEN_STATUS | WRITE_FINISHING | WRITE_DONE\nconst WRITE_QUEUED_AND_UNDRAINED = WRITE_QUEUED | WRITE_UNDRAINED\nconst WRITE_QUEUED_AND_ACTIVE = WRITE_QUEUED | WRITE_ACTIVE\nconst WRITE_DRAIN_STATUS = WRITE_QUEUED | WRITE_UNDRAINED | OPEN_STATUS | WRITE_ACTIVE\nconst WRITE_STATUS = OPEN_STATUS | WRITE_ACTIVE | WRITE_QUEUED | WRITE_CORKED\nconst WRITE_PRIMARY_AND_ACTIVE = WRITE_PRIMARY | WRITE_ACTIVE\nconst WRITE_ACTIVE_AND_WRITING = WRITE_ACTIVE | WRITE_WRITING\nconst WRITE_FINISHING_STATUS = OPEN_STATUS | WRITE_FINISHING | WRITE_QUEUED_AND_ACTIVE | WRITE_DONE\nconst WRITE_BACKPRESSURE_STATUS = WRITE_UNDRAINED | DESTROY_STATUS | WRITE_FINISHING | WRITE_DONE\nconst WRITE_UPDATE_SYNC_STATUS = WRITE_UPDATING | OPEN_STATUS | WRITE_NEXT_TICK | WRITE_PRIMARY\nconst WRITE_DROP_DATA = WRITE_FINISHING | WRITE_DONE | DESTROY_STATUS\n\nconst asyncIterator = Symbol.asyncIterator || Symbol('asyncIterator')\n\nclass WritableState {\n  constructor (stream, { highWaterMark = 16384, map = null, mapWritable, byteLength, byteLengthWritable } = {}) {\n    this.stream = stream\n    this.queue = new FIFO()\n    this.highWaterMark = highWaterMark\n    this.buffered = 0\n    this.error = null\n    this.pipeline = null\n    this.drains = null // if we add more seldomly used helpers we might them into a subobject so its a single ptr\n    this.byteLength = byteLengthWritable || byteLength || defaultByteLength\n    this.map = mapWritable || map\n    this.afterWrite = afterWrite.bind(this)\n    this.afterUpdateNextTick = updateWriteNT.bind(this)\n  }\n\n  get ended () {\n    return (this.stream._duplexState & WRITE_DONE) !== 0\n  }\n\n  push (data) {\n    if ((this.stream._duplexState & WRITE_DROP_DATA) !== 0) return false\n    if (this.map !== null) data = this.map(data)\n\n    this.buffered += this.byteLength(data)\n    this.queue.push(data)\n\n    if (this.buffered < this.highWaterMark) {\n      this.stream._duplexState |= WRITE_QUEUED\n      return true\n    }\n\n    this.stream._duplexState |= WRITE_QUEUED_AND_UNDRAINED\n    return false\n  }\n\n  shift () {\n    const data = this.queue.shift()\n\n    this.buffered -= this.byteLength(data)\n    if (this.buffered === 0) this.stream._duplexState &= WRITE_NOT_QUEUED\n\n    return data\n  }\n\n  end (data) {\n    if (typeof data === 'function') this.stream.once('finish', data)\n    else if (data !== undefined && data !== null) this.push(data)\n    this.stream._duplexState = (this.stream._duplexState | WRITE_FINISHING) & WRITE_NON_PRIMARY\n  }\n\n  autoBatch (data, cb) {\n    const buffer = []\n    const stream = this.stream\n\n    buffer.push(data)\n    while ((stream._duplexState & WRITE_STATUS) === WRITE_QUEUED_AND_ACTIVE) {\n      buffer.push(stream._writableState.shift())\n    }\n\n    if ((stream._duplexState & OPEN_STATUS) !== 0) return cb(null)\n    stream._writev(buffer, cb)\n  }\n\n  update () {\n    const stream = this.stream\n\n    stream._duplexState |= WRITE_UPDATING\n\n    do {\n      while ((stream._duplexState & WRITE_STATUS) === WRITE_QUEUED) {\n        const data = this.shift()\n        stream._duplexState |= WRITE_ACTIVE_AND_WRITING\n        stream._write(data, this.afterWrite)\n      }\n\n      if ((stream._duplexState & WRITE_PRIMARY_AND_ACTIVE) === 0) this.updateNonPrimary()\n    } while (this.continueUpdate() === true)\n\n    stream._duplexState &= WRITE_NOT_UPDATING\n  }\n\n  updateNonPrimary () {\n    const stream = this.stream\n\n    if ((stream._duplexState & WRITE_FINISHING_STATUS) === WRITE_FINISHING) {\n      stream._duplexState = stream._duplexState | WRITE_ACTIVE\n      stream._final(afterFinal.bind(this))\n      return\n    }\n\n    if ((stream._duplexState & DESTROY_STATUS) === DESTROYING) {\n      if ((stream._duplexState & ACTIVE_OR_TICKING) === 0) {\n        stream._duplexState |= ACTIVE\n        stream._destroy(afterDestroy.bind(this))\n      }\n      return\n    }\n\n    if ((stream._duplexState & IS_OPENING) === OPENING) {\n      stream._duplexState = (stream._duplexState | ACTIVE) & NOT_OPENING\n      stream._open(afterOpen.bind(this))\n    }\n  }\n\n  continueUpdate () {\n    if ((this.stream._duplexState & WRITE_NEXT_TICK) === 0) return false\n    this.stream._duplexState &= WRITE_NOT_NEXT_TICK\n    return true\n  }\n\n  updateCallback () {\n    if ((this.stream._duplexState & WRITE_UPDATE_SYNC_STATUS) === WRITE_PRIMARY) this.update()\n    else this.updateNextTick()\n  }\n\n  updateNextTick () {\n    if ((this.stream._duplexState & WRITE_NEXT_TICK) !== 0) return\n    this.stream._duplexState |= WRITE_NEXT_TICK\n    if ((this.stream._duplexState & WRITE_UPDATING) === 0) qmt(this.afterUpdateNextTick)\n  }\n}\n\nclass ReadableState {\n  constructor (stream, { highWaterMark = 16384, map = null, mapReadable, byteLength, byteLengthReadable } = {}) {\n    this.stream = stream\n    this.queue = new FIFO()\n    this.highWaterMark = highWaterMark === 0 ? 1 : highWaterMark\n    this.buffered = 0\n    this.readAhead = highWaterMark > 0\n    this.error = null\n    this.pipeline = null\n    this.byteLength = byteLengthReadable || byteLength || defaultByteLength\n    this.map = mapReadable || map\n    this.pipeTo = null\n    this.afterRead = afterRead.bind(this)\n    this.afterUpdateNextTick = updateReadNT.bind(this)\n  }\n\n  get ended () {\n    return (this.stream._duplexState & READ_DONE) !== 0\n  }\n\n  pipe (pipeTo, cb) {\n    if (this.pipeTo !== null) throw new Error('Can only pipe to one destination')\n    if (typeof cb !== 'function') cb = null\n\n    this.stream._duplexState |= READ_PIPE_DRAINED\n    this.pipeTo = pipeTo\n    this.pipeline = new Pipeline(this.stream, pipeTo, cb)\n\n    if (cb) this.stream.on('error', noop) // We already error handle this so supress crashes\n\n    if (isStreamx(pipeTo)) {\n      pipeTo._writableState.pipeline = this.pipeline\n      if (cb) pipeTo.on('error', noop) // We already error handle this so supress crashes\n      pipeTo.on('finish', this.pipeline.finished.bind(this.pipeline)) // TODO: just call finished from pipeTo itself\n    } else {\n      const onerror = this.pipeline.done.bind(this.pipeline, pipeTo)\n      const onclose = this.pipeline.done.bind(this.pipeline, pipeTo, null) // onclose has a weird bool arg\n      pipeTo.on('error', onerror)\n      pipeTo.on('close', onclose)\n      pipeTo.on('finish', this.pipeline.finished.bind(this.pipeline))\n    }\n\n    pipeTo.on('drain', afterDrain.bind(this))\n    this.stream.emit('piping', pipeTo)\n    pipeTo.emit('pipe', this.stream)\n  }\n\n  push (data) {\n    const stream = this.stream\n\n    if (data === null) {\n      this.highWaterMark = 0\n      stream._duplexState = (stream._duplexState | READ_ENDING) & READ_NON_PRIMARY_AND_PUSHED\n      return false\n    }\n\n    if (this.map !== null) {\n      data = this.map(data)\n      if (data === null) {\n        stream._duplexState &= READ_PUSHED\n        return this.buffered < this.highWaterMark\n      }\n    }\n\n    this.buffered += this.byteLength(data)\n    this.queue.push(data)\n\n    stream._duplexState = (stream._duplexState | READ_QUEUED) & READ_PUSHED\n\n    return this.buffered < this.highWaterMark\n  }\n\n  shift () {\n    const data = this.queue.shift()\n\n    this.buffered -= this.byteLength(data)\n    if (this.buffered === 0) this.stream._duplexState &= READ_NOT_QUEUED\n    return data\n  }\n\n  unshift (data) {\n    const pending = [this.map !== null ? this.map(data) : data]\n    while (this.buffered > 0) pending.push(this.shift())\n\n    for (let i = 0; i < pending.length - 1; i++) {\n      const data = pending[i]\n      this.buffered += this.byteLength(data)\n      this.queue.push(data)\n    }\n\n    this.push(pending[pending.length - 1])\n  }\n\n  read () {\n    const stream = this.stream\n\n    if ((stream._duplexState & READ_STATUS) === READ_QUEUED) {\n      const data = this.shift()\n      if (this.pipeTo !== null && this.pipeTo.write(data) === false) stream._duplexState &= READ_PIPE_NOT_DRAINED\n      if ((stream._duplexState & READ_EMIT_DATA) !== 0) stream.emit('data', data)\n      return data\n    }\n\n    if (this.readAhead === false) {\n      stream._duplexState |= READ_READ_AHEAD\n      this.updateNextTick()\n    }\n\n    return null\n  }\n\n  drain () {\n    const stream = this.stream\n\n    while ((stream._duplexState & READ_STATUS) === READ_QUEUED && (stream._duplexState & READ_FLOWING) !== 0) {\n      const data = this.shift()\n      if (this.pipeTo !== null && this.pipeTo.write(data) === false) stream._duplexState &= READ_PIPE_NOT_DRAINED\n      if ((stream._duplexState & READ_EMIT_DATA) !== 0) stream.emit('data', data)\n    }\n  }\n\n  update () {\n    const stream = this.stream\n\n    stream._duplexState |= READ_UPDATING\n\n    do {\n      this.drain()\n\n      while (this.buffered < this.highWaterMark && (stream._duplexState & SHOULD_NOT_READ) === READ_READ_AHEAD) {\n        stream._duplexState |= READ_ACTIVE_AND_NEEDS_PUSH\n        stream._read(this.afterRead)\n        this.drain()\n      }\n\n      if ((stream._duplexState & READ_READABLE_STATUS) === READ_EMIT_READABLE_AND_QUEUED) {\n        stream._duplexState |= READ_EMITTED_READABLE\n        stream.emit('readable')\n      }\n\n      if ((stream._duplexState & READ_PRIMARY_AND_ACTIVE) === 0) this.updateNonPrimary()\n    } while (this.continueUpdate() === true)\n\n    stream._duplexState &= READ_NOT_UPDATING\n  }\n\n  updateNonPrimary () {\n    const stream = this.stream\n\n    if ((stream._duplexState & READ_ENDING_STATUS) === READ_ENDING) {\n      stream._duplexState = (stream._duplexState | READ_DONE) & READ_NOT_ENDING\n      stream.emit('end')\n      if ((stream._duplexState & AUTO_DESTROY) === DONE) stream._duplexState |= DESTROYING\n      if (this.pipeTo !== null) this.pipeTo.end()\n    }\n\n    if ((stream._duplexState & DESTROY_STATUS) === DESTROYING) {\n      if ((stream._duplexState & ACTIVE_OR_TICKING) === 0) {\n        stream._duplexState |= ACTIVE\n        stream._destroy(afterDestroy.bind(this))\n      }\n      return\n    }\n\n    if ((stream._duplexState & IS_OPENING) === OPENING) {\n      stream._duplexState = (stream._duplexState | ACTIVE) & NOT_OPENING\n      stream._open(afterOpen.bind(this))\n    }\n  }\n\n  continueUpdate () {\n    if ((this.stream._duplexState & READ_NEXT_TICK) === 0) return false\n    this.stream._duplexState &= READ_NOT_NEXT_TICK\n    return true\n  }\n\n  updateCallback () {\n    if ((this.stream._duplexState & READ_UPDATE_SYNC_STATUS) === READ_PRIMARY) this.update()\n    else this.updateNextTick()\n  }\n\n  updateNextTickIfOpen () {\n    if ((this.stream._duplexState & READ_NEXT_TICK_OR_OPENING) !== 0) return\n    this.stream._duplexState |= READ_NEXT_TICK\n    if ((this.stream._duplexState & READ_UPDATING) === 0) qmt(this.afterUpdateNextTick)\n  }\n\n  updateNextTick () {\n    if ((this.stream._duplexState & READ_NEXT_TICK) !== 0) return\n    this.stream._duplexState |= READ_NEXT_TICK\n    if ((this.stream._duplexState & READ_UPDATING) === 0) qmt(this.afterUpdateNextTick)\n  }\n}\n\nclass TransformState {\n  constructor (stream) {\n    this.data = null\n    this.afterTransform = afterTransform.bind(stream)\n    this.afterFinal = null\n  }\n}\n\nclass Pipeline {\n  constructor (src, dst, cb) {\n    this.from = src\n    this.to = dst\n    this.afterPipe = cb\n    this.error = null\n    this.pipeToFinished = false\n  }\n\n  finished () {\n    this.pipeToFinished = true\n  }\n\n  done (stream, err) {\n    if (err) this.error = err\n\n    if (stream === this.to) {\n      this.to = null\n\n      if (this.from !== null) {\n        if ((this.from._duplexState & READ_DONE) === 0 || !this.pipeToFinished) {\n          this.from.destroy(this.error || new Error('Writable stream closed prematurely'))\n        }\n        return\n      }\n    }\n\n    if (stream === this.from) {\n      this.from = null\n\n      if (this.to !== null) {\n        if ((stream._duplexState & READ_DONE) === 0) {\n          this.to.destroy(this.error || new Error('Readable stream closed before ending'))\n        }\n        return\n      }\n    }\n\n    if (this.afterPipe !== null) this.afterPipe(this.error)\n    this.to = this.from = this.afterPipe = null\n  }\n}\n\nfunction afterDrain () {\n  this.stream._duplexState |= READ_PIPE_DRAINED\n  this.updateCallback()\n}\n\nfunction afterFinal (err) {\n  const stream = this.stream\n  if (err) stream.destroy(err)\n  if ((stream._duplexState & DESTROY_STATUS) === 0) {\n    stream._duplexState |= WRITE_DONE\n    stream.emit('finish')\n  }\n  if ((stream._duplexState & AUTO_DESTROY) === DONE) {\n    stream._duplexState |= DESTROYING\n  }\n\n  stream._duplexState &= WRITE_NOT_FINISHING\n\n  // no need to wait the extra tick here, so we short circuit that\n  if ((stream._duplexState & WRITE_UPDATING) === 0) this.update()\n  else this.updateNextTick()\n}\n\nfunction afterDestroy (err) {\n  const stream = this.stream\n\n  if (!err && this.error !== STREAM_DESTROYED) err = this.error\n  if (err) stream.emit('error', err)\n  stream._duplexState |= DESTROYED\n  stream.emit('close')\n\n  const rs = stream._readableState\n  const ws = stream._writableState\n\n  if (rs !== null && rs.pipeline !== null) rs.pipeline.done(stream, err)\n\n  if (ws !== null) {\n    while (ws.drains !== null && ws.drains.length > 0) ws.drains.shift().resolve(false)\n    if (ws.pipeline !== null) ws.pipeline.done(stream, err)\n  }\n}\n\nfunction afterWrite (err) {\n  const stream = this.stream\n\n  if (err) stream.destroy(err)\n  stream._duplexState &= WRITE_NOT_ACTIVE\n\n  if (this.drains !== null) tickDrains(this.drains)\n\n  if ((stream._duplexState & WRITE_DRAIN_STATUS) === WRITE_UNDRAINED) {\n    stream._duplexState &= WRITE_DRAINED\n    if ((stream._duplexState & WRITE_EMIT_DRAIN) === WRITE_EMIT_DRAIN) {\n      stream.emit('drain')\n    }\n  }\n\n  this.updateCallback()\n}\n\nfunction afterRead (err) {\n  if (err) this.stream.destroy(err)\n  this.stream._duplexState &= READ_NOT_ACTIVE\n  if (this.readAhead === false && (this.stream._duplexState & READ_RESUMED) === 0) this.stream._duplexState &= READ_NO_READ_AHEAD\n  this.updateCallback()\n}\n\nfunction updateReadNT () {\n  if ((this.stream._duplexState & READ_UPDATING) === 0) {\n    this.stream._duplexState &= READ_NOT_NEXT_TICK\n    this.update()\n  }\n}\n\nfunction updateWriteNT () {\n  if ((this.stream._duplexState & WRITE_UPDATING) === 0) {\n    this.stream._duplexState &= WRITE_NOT_NEXT_TICK\n    this.update()\n  }\n}\n\nfunction tickDrains (drains) {\n  for (let i = 0; i < drains.length; i++) {\n    // drains.writes are monotonic, so if one is 0 its always the first one\n    if (--drains[i].writes === 0) {\n      drains.shift().resolve(true)\n      i--\n    }\n  }\n}\n\nfunction afterOpen (err) {\n  const stream = this.stream\n\n  if (err) stream.destroy(err)\n\n  if ((stream._duplexState & DESTROYING) === 0) {\n    if ((stream._duplexState & READ_PRIMARY_STATUS) === 0) stream._duplexState |= READ_PRIMARY\n    if ((stream._duplexState & WRITE_PRIMARY_STATUS) === 0) stream._duplexState |= WRITE_PRIMARY\n    stream.emit('open')\n  }\n\n  stream._duplexState &= NOT_ACTIVE\n\n  if (stream._writableState !== null) {\n    stream._writableState.updateCallback()\n  }\n\n  if (stream._readableState !== null) {\n    stream._readableState.updateCallback()\n  }\n}\n\nfunction afterTransform (err, data) {\n  if (data !== undefined && data !== null) this.push(data)\n  this._writableState.afterWrite(err)\n}\n\nfunction newListener (name) {\n  if (this._readableState !== null) {\n    if (name === 'data') {\n      this._duplexState |= (READ_EMIT_DATA | READ_RESUMED_READ_AHEAD)\n      this._readableState.updateNextTick()\n    }\n    if (name === 'readable') {\n      this._duplexState |= READ_EMIT_READABLE\n      this._readableState.updateNextTick()\n    }\n  }\n\n  if (this._writableState !== null) {\n    if (name === 'drain') {\n      this._duplexState |= WRITE_EMIT_DRAIN\n      this._writableState.updateNextTick()\n    }\n  }\n}\n\nclass Stream extends EventEmitter {\n  constructor (opts) {\n    super()\n\n    this._duplexState = 0\n    this._readableState = null\n    this._writableState = null\n\n    if (opts) {\n      if (opts.open) this._open = opts.open\n      if (opts.destroy) this._destroy = opts.destroy\n      if (opts.predestroy) this._predestroy = opts.predestroy\n      if (opts.signal) {\n        opts.signal.addEventListener('abort', abort.bind(this))\n      }\n    }\n\n    this.on('newListener', newListener)\n  }\n\n  _open (cb) {\n    cb(null)\n  }\n\n  _destroy (cb) {\n    cb(null)\n  }\n\n  _predestroy () {\n    // does nothing\n  }\n\n  get readable () {\n    return this._readableState !== null ? true : undefined\n  }\n\n  get writable () {\n    return this._writableState !== null ? true : undefined\n  }\n\n  get destroyed () {\n    return (this._duplexState & DESTROYED) !== 0\n  }\n\n  get destroying () {\n    return (this._duplexState & DESTROY_STATUS) !== 0\n  }\n\n  destroy (err) {\n    if ((this._duplexState & DESTROY_STATUS) === 0) {\n      if (!err) err = STREAM_DESTROYED\n      this._duplexState = (this._duplexState | DESTROYING) & NON_PRIMARY\n\n      if (this._readableState !== null) {\n        this._readableState.highWaterMark = 0\n        this._readableState.error = err\n      }\n      if (this._writableState !== null) {\n        this._writableState.highWaterMark = 0\n        this._writableState.error = err\n      }\n\n      this._duplexState |= PREDESTROYING\n      this._predestroy()\n      this._duplexState &= NOT_PREDESTROYING\n\n      if (this._readableState !== null) this._readableState.updateNextTick()\n      if (this._writableState !== null) this._writableState.updateNextTick()\n    }\n  }\n}\n\nclass Readable extends Stream {\n  constructor (opts) {\n    super(opts)\n\n    this._duplexState |= OPENING | WRITE_DONE | READ_READ_AHEAD\n    this._readableState = new ReadableState(this, opts)\n\n    if (opts) {\n      if (this._readableState.readAhead === false) this._duplexState &= READ_NO_READ_AHEAD\n      if (opts.read) this._read = opts.read\n      if (opts.eagerOpen) this._readableState.updateNextTick()\n      if (opts.encoding) this.setEncoding(opts.encoding)\n    }\n  }\n\n  setEncoding (encoding) {\n    const dec = new TextDecoder(encoding)\n    const map = this._readableState.map || echo\n    this._readableState.map = mapOrSkip\n    return this\n\n    function mapOrSkip (data) {\n      const next = dec.push(data)\n      return next === '' && (data.byteLength !== 0 || dec.remaining > 0) ? null : map(next)\n    }\n  }\n\n  _read (cb) {\n    cb(null)\n  }\n\n  pipe (dest, cb) {\n    this._readableState.updateNextTick()\n    this._readableState.pipe(dest, cb)\n    return dest\n  }\n\n  read () {\n    this._readableState.updateNextTick()\n    return this._readableState.read()\n  }\n\n  push (data) {\n    this._readableState.updateNextTickIfOpen()\n    return this._readableState.push(data)\n  }\n\n  unshift (data) {\n    this._readableState.updateNextTickIfOpen()\n    return this._readableState.unshift(data)\n  }\n\n  resume () {\n    this._duplexState |= READ_RESUMED_READ_AHEAD\n    this._readableState.updateNextTick()\n    return this\n  }\n\n  pause () {\n    this._duplexState &= (this._readableState.readAhead === false ? READ_PAUSED_NO_READ_AHEAD : READ_PAUSED)\n    return this\n  }\n\n  static _fromAsyncIterator (ite, opts) {\n    let destroy\n\n    const rs = new Readable({\n      ...opts,\n      read (cb) {\n        ite.next().then(push).then(cb.bind(null, null)).catch(cb)\n      },\n      predestroy () {\n        destroy = ite.return()\n      },\n      destroy (cb) {\n        if (!destroy) return cb(null)\n        destroy.then(cb.bind(null, null)).catch(cb)\n      }\n    })\n\n    return rs\n\n    function push (data) {\n      if (data.done) rs.push(null)\n      else rs.push(data.value)\n    }\n  }\n\n  static from (data, opts) {\n    if (isReadStreamx(data)) return data\n    if (data[asyncIterator]) return this._fromAsyncIterator(data[asyncIterator](), opts)\n    if (!Array.isArray(data)) data = data === undefined ? [] : [data]\n\n    let i = 0\n    return new Readable({\n      ...opts,\n      read (cb) {\n        this.push(i === data.length ? null : data[i++])\n        cb(null)\n      }\n    })\n  }\n\n  static isBackpressured (rs) {\n    return (rs._duplexState & READ_BACKPRESSURE_STATUS) !== 0 || rs._readableState.buffered >= rs._readableState.highWaterMark\n  }\n\n  static isPaused (rs) {\n    return (rs._duplexState & READ_RESUMED) === 0\n  }\n\n  [asyncIterator] () {\n    const stream = this\n\n    let error = null\n    let promiseResolve = null\n    let promiseReject = null\n\n    this.on('error', (err) => { error = err })\n    this.on('readable', onreadable)\n    this.on('close', onclose)\n\n    return {\n      [asyncIterator] () {\n        return this\n      },\n      next () {\n        return new Promise(function (resolve, reject) {\n          promiseResolve = resolve\n          promiseReject = reject\n          const data = stream.read()\n          if (data !== null) ondata(data)\n          else if ((stream._duplexState & DESTROYED) !== 0) ondata(null)\n        })\n      },\n      return () {\n        return destroy(null)\n      },\n      throw (err) {\n        return destroy(err)\n      }\n    }\n\n    function onreadable () {\n      if (promiseResolve !== null) ondata(stream.read())\n    }\n\n    function onclose () {\n      if (promiseResolve !== null) ondata(null)\n    }\n\n    function ondata (data) {\n      if (promiseReject === null) return\n      if (error) promiseReject(error)\n      else if (data === null && (stream._duplexState & READ_DONE) === 0) promiseReject(STREAM_DESTROYED)\n      else promiseResolve({ value: data, done: data === null })\n      promiseReject = promiseResolve = null\n    }\n\n    function destroy (err) {\n      stream.destroy(err)\n      return new Promise((resolve, reject) => {\n        if (stream._duplexState & DESTROYED) return resolve({ value: undefined, done: true })\n        stream.once('close', function () {\n          if (err) reject(err)\n          else resolve({ value: undefined, done: true })\n        })\n      })\n    }\n  }\n}\n\nclass Writable extends Stream {\n  constructor (opts) {\n    super(opts)\n\n    this._duplexState |= OPENING | READ_DONE\n    this._writableState = new WritableState(this, opts)\n\n    if (opts) {\n      if (opts.writev) this._writev = opts.writev\n      if (opts.write) this._write = opts.write\n      if (opts.final) this._final = opts.final\n      if (opts.eagerOpen) this._writableState.updateNextTick()\n    }\n  }\n\n  cork () {\n    this._duplexState |= WRITE_CORKED\n  }\n\n  uncork () {\n    this._duplexState &= WRITE_NOT_CORKED\n    this._writableState.updateNextTick()\n  }\n\n  _writev (batch, cb) {\n    cb(null)\n  }\n\n  _write (data, cb) {\n    this._writableState.autoBatch(data, cb)\n  }\n\n  _final (cb) {\n    cb(null)\n  }\n\n  static isBackpressured (ws) {\n    return (ws._duplexState & WRITE_BACKPRESSURE_STATUS) !== 0\n  }\n\n  static drained (ws) {\n    if (ws.destroyed) return Promise.resolve(false)\n    const state = ws._writableState\n    const pending = (isWritev(ws) ? Math.min(1, state.queue.length) : state.queue.length)\n    const writes = pending + ((ws._duplexState & WRITE_WRITING) ? 1 : 0)\n    if (writes === 0) return Promise.resolve(true)\n    if (state.drains === null) state.drains = []\n    return new Promise((resolve) => {\n      state.drains.push({ writes, resolve })\n    })\n  }\n\n  write (data) {\n    this._writableState.updateNextTick()\n    return this._writableState.push(data)\n  }\n\n  end (data) {\n    this._writableState.updateNextTick()\n    this._writableState.end(data)\n    return this\n  }\n}\n\nclass Duplex extends Readable { // and Writable\n  constructor (opts) {\n    super(opts)\n\n    this._duplexState = OPENING | (this._duplexState & READ_READ_AHEAD)\n    this._writableState = new WritableState(this, opts)\n\n    if (opts) {\n      if (opts.writev) this._writev = opts.writev\n      if (opts.write) this._write = opts.write\n      if (opts.final) this._final = opts.final\n    }\n  }\n\n  cork () {\n    this._duplexState |= WRITE_CORKED\n  }\n\n  uncork () {\n    this._duplexState &= WRITE_NOT_CORKED\n    this._writableState.updateNextTick()\n  }\n\n  _writev (batch, cb) {\n    cb(null)\n  }\n\n  _write (data, cb) {\n    this._writableState.autoBatch(data, cb)\n  }\n\n  _final (cb) {\n    cb(null)\n  }\n\n  write (data) {\n    this._writableState.updateNextTick()\n    return this._writableState.push(data)\n  }\n\n  end (data) {\n    this._writableState.updateNextTick()\n    this._writableState.end(data)\n    return this\n  }\n}\n\nclass Transform extends Duplex {\n  constructor (opts) {\n    super(opts)\n    this._transformState = new TransformState(this)\n\n    if (opts) {\n      if (opts.transform) this._transform = opts.transform\n      if (opts.flush) this._flush = opts.flush\n    }\n  }\n\n  _write (data, cb) {\n    if (this._readableState.buffered >= this._readableState.highWaterMark) {\n      this._transformState.data = data\n    } else {\n      this._transform(data, this._transformState.afterTransform)\n    }\n  }\n\n  _read (cb) {\n    if (this._transformState.data !== null) {\n      const data = this._transformState.data\n      this._transformState.data = null\n      cb(null)\n      this._transform(data, this._transformState.afterTransform)\n    } else {\n      cb(null)\n    }\n  }\n\n  destroy (err) {\n    super.destroy(err)\n    if (this._transformState.data !== null) {\n      this._transformState.data = null\n      this._transformState.afterTransform()\n    }\n  }\n\n  _transform (data, cb) {\n    cb(null, data)\n  }\n\n  _flush (cb) {\n    cb(null)\n  }\n\n  _final (cb) {\n    this._transformState.afterFinal = cb\n    this._flush(transformAfterFlush.bind(this))\n  }\n}\n\nclass PassThrough extends Transform {}\n\nfunction transformAfterFlush (err, data) {\n  const cb = this._transformState.afterFinal\n  if (err) return cb(err)\n  if (data !== null && data !== undefined) this.push(data)\n  this.push(null)\n  cb(null)\n}\n\nfunction pipelinePromise (...streams) {\n  return new Promise((resolve, reject) => {\n    return pipeline(...streams, (err) => {\n      if (err) return reject(err)\n      resolve()\n    })\n  })\n}\n\nfunction pipeline (stream, ...streams) {\n  const all = Array.isArray(stream) ? [...stream, ...streams] : [stream, ...streams]\n  const done = (all.length && typeof all[all.length - 1] === 'function') ? all.pop() : null\n\n  if (all.length < 2) throw new Error('Pipeline requires at least 2 streams')\n\n  let src = all[0]\n  let dest = null\n  let error = null\n\n  for (let i = 1; i < all.length; i++) {\n    dest = all[i]\n\n    if (isStreamx(src)) {\n      src.pipe(dest, onerror)\n    } else {\n      errorHandle(src, true, i > 1, onerror)\n      src.pipe(dest)\n    }\n\n    src = dest\n  }\n\n  if (done) {\n    let fin = false\n\n    const autoDestroy = isStreamx(dest) || !!(dest._writableState && dest._writableState.autoDestroy)\n\n    dest.on('error', (err) => {\n      if (error === null) error = err\n    })\n\n    dest.on('finish', () => {\n      fin = true\n      if (!autoDestroy) done(error)\n    })\n\n    if (autoDestroy) {\n      dest.on('close', () => done(error || (fin ? null : PREMATURE_CLOSE)))\n    }\n  }\n\n  return dest\n\n  function errorHandle (s, rd, wr, onerror) {\n    s.on('error', onerror)\n    s.on('close', onclose)\n\n    function onclose () {\n      if (rd && s._readableState && !s._readableState.ended) return onerror(PREMATURE_CLOSE)\n      if (wr && s._writableState && !s._writableState.ended) return onerror(PREMATURE_CLOSE)\n    }\n  }\n\n  function onerror (err) {\n    if (!err || error) return\n    error = err\n\n    for (const s of all) {\n      s.destroy(err)\n    }\n  }\n}\n\nfunction echo (s) {\n  return s\n}\n\nfunction isStream (stream) {\n  return !!stream._readableState || !!stream._writableState\n}\n\nfunction isStreamx (stream) {\n  return typeof stream._duplexState === 'number' && isStream(stream)\n}\n\nfunction isEnded (stream) {\n  return !!stream._readableState && stream._readableState.ended\n}\n\nfunction isFinished (stream) {\n  return !!stream._writableState && stream._writableState.ended\n}\n\nfunction getStreamError (stream, opts = {}) {\n  const err = (stream._readableState && stream._readableState.error) || (stream._writableState && stream._writableState.error)\n\n  // avoid implicit errors by default\n  return (!opts.all && err === STREAM_DESTROYED) ? null : err\n}\n\nfunction isReadStreamx (stream) {\n  return isStreamx(stream) && stream.readable\n}\n\nfunction isDisturbed (stream) {\n  return (stream._duplexState & OPENING) !== OPENING || (stream._duplexState & ACTIVE_OR_TICKING) !== 0\n}\n\nfunction isTypedArray (data) {\n  return typeof data === 'object' && data !== null && typeof data.byteLength === 'number'\n}\n\nfunction defaultByteLength (data) {\n  return isTypedArray(data) ? data.byteLength : 1024\n}\n\nfunction noop () {}\n\nfunction abort () {\n  this.destroy(new Error('Stream aborted.'))\n}\n\nfunction isWritev (s) {\n  return s._writev !== Writable.prototype._writev && s._writev !== Duplex.prototype._writev\n}\n\nmodule.exports = {\n  pipeline,\n  pipelinePromise,\n  isStream,\n  isStreamx,\n  isEnded,\n  isFinished,\n  isDisturbed,\n  getStreamError,\n  Stream,\n  Writable,\n  Readable,\n  Duplex,\n  Transform,\n  // Export PassThrough for compatibility with Node.js core's stream module\n  PassThrough\n}\n", "const b4a = require('b4a')\n\nconst ZEROS = '0000000000000000000'\nconst SEVENS = '7777777777777777777'\nconst ZERO_OFFSET = '0'.charCodeAt(0)\nconst USTAR_MAGIC = b4a.from([0x75, 0x73, 0x74, 0x61, 0x72, 0x00]) // ustar\\x00\nconst USTAR_VER = b4a.from([ZERO_OFFSET, ZERO_OFFSET])\nconst GNU_MAGIC = b4a.from([0x75, 0x73, 0x74, 0x61, 0x72, 0x20]) // ustar\\x20\nconst GNU_VER = b4a.from([0x20, 0x00])\nconst MASK = 0o7777\nconst MAGIC_OFFSET = 257\nconst VERSION_OFFSET = 263\n\nexports.decodeLongPath = function decodeLongPath (buf, encoding) {\n  return decodeStr(buf, 0, buf.length, encoding)\n}\n\nexports.encodePax = function encodePax (opts) { // TODO: encode more stuff in pax\n  let result = ''\n  if (opts.name) result += addLength(' path=' + opts.name + '\\n')\n  if (opts.linkname) result += addLength(' linkpath=' + opts.linkname + '\\n')\n  const pax = opts.pax\n  if (pax) {\n    for (const key in pax) {\n      result += addLength(' ' + key + '=' + pax[key] + '\\n')\n    }\n  }\n  return b4a.from(result)\n}\n\nexports.decodePax = function decodePax (buf) {\n  const result = {}\n\n  while (buf.length) {\n    let i = 0\n    while (i < buf.length && buf[i] !== 32) i++\n    const len = parseInt(b4a.toString(buf.subarray(0, i)), 10)\n    if (!len) return result\n\n    const b = b4a.toString(buf.subarray(i + 1, len - 1))\n    const keyIndex = b.indexOf('=')\n    if (keyIndex === -1) return result\n    result[b.slice(0, keyIndex)] = b.slice(keyIndex + 1)\n\n    buf = buf.subarray(len)\n  }\n\n  return result\n}\n\nexports.encode = function encode (opts) {\n  const buf = b4a.alloc(512)\n  let name = opts.name\n  let prefix = ''\n\n  if (opts.typeflag === 5 && name[name.length - 1] !== '/') name += '/'\n  if (b4a.byteLength(name) !== name.length) return null // utf-8\n\n  while (b4a.byteLength(name) > 100) {\n    const i = name.indexOf('/')\n    if (i === -1) return null\n    prefix += prefix ? '/' + name.slice(0, i) : name.slice(0, i)\n    name = name.slice(i + 1)\n  }\n\n  if (b4a.byteLength(name) > 100 || b4a.byteLength(prefix) > 155) return null\n  if (opts.linkname && b4a.byteLength(opts.linkname) > 100) return null\n\n  b4a.write(buf, name)\n  b4a.write(buf, encodeOct(opts.mode & MASK, 6), 100)\n  b4a.write(buf, encodeOct(opts.uid, 6), 108)\n  b4a.write(buf, encodeOct(opts.gid, 6), 116)\n  encodeSize(opts.size, buf, 124)\n  b4a.write(buf, encodeOct((opts.mtime.getTime() / 1000) | 0, 11), 136)\n\n  buf[156] = ZERO_OFFSET + toTypeflag(opts.type)\n\n  if (opts.linkname) b4a.write(buf, opts.linkname, 157)\n\n  b4a.copy(USTAR_MAGIC, buf, MAGIC_OFFSET)\n  b4a.copy(USTAR_VER, buf, VERSION_OFFSET)\n  if (opts.uname) b4a.write(buf, opts.uname, 265)\n  if (opts.gname) b4a.write(buf, opts.gname, 297)\n  b4a.write(buf, encodeOct(opts.devmajor || 0, 6), 329)\n  b4a.write(buf, encodeOct(opts.devminor || 0, 6), 337)\n\n  if (prefix) b4a.write(buf, prefix, 345)\n\n  b4a.write(buf, encodeOct(cksum(buf), 6), 148)\n\n  return buf\n}\n\nexports.decode = function decode (buf, filenameEncoding, allowUnknownFormat) {\n  let typeflag = buf[156] === 0 ? 0 : buf[156] - ZERO_OFFSET\n\n  let name = decodeStr(buf, 0, 100, filenameEncoding)\n  const mode = decodeOct(buf, 100, 8)\n  const uid = decodeOct(buf, 108, 8)\n  const gid = decodeOct(buf, 116, 8)\n  const size = decodeOct(buf, 124, 12)\n  const mtime = decodeOct(buf, 136, 12)\n  const type = toType(typeflag)\n  const linkname = buf[157] === 0 ? null : decodeStr(buf, 157, 100, filenameEncoding)\n  const uname = decodeStr(buf, 265, 32)\n  const gname = decodeStr(buf, 297, 32)\n  const devmajor = decodeOct(buf, 329, 8)\n  const devminor = decodeOct(buf, 337, 8)\n\n  const c = cksum(buf)\n\n  // checksum is still initial value if header was null.\n  if (c === 8 * 32) return null\n\n  // valid checksum\n  if (c !== decodeOct(buf, 148, 8)) throw new Error('Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?')\n\n  if (isUSTAR(buf)) {\n    // ustar (posix) format.\n    // prepend prefix, if present.\n    if (buf[345]) name = decodeStr(buf, 345, 155, filenameEncoding) + '/' + name\n  } else if (isGNU(buf)) {\n    // 'gnu'/'oldgnu' format. Similar to ustar, but has support for incremental and\n    // multi-volume tarballs.\n  } else {\n    if (!allowUnknownFormat) {\n      throw new Error('Invalid tar header: unknown format.')\n    }\n  }\n\n  // to support old tar versions that use trailing / to indicate dirs\n  if (typeflag === 0 && name && name[name.length - 1] === '/') typeflag = 5\n\n  return {\n    name,\n    mode,\n    uid,\n    gid,\n    size,\n    mtime: new Date(1000 * mtime),\n    type,\n    linkname,\n    uname,\n    gname,\n    devmajor,\n    devminor,\n    pax: null\n  }\n}\n\nfunction isUSTAR (buf) {\n  return b4a.equals(USTAR_MAGIC, buf.subarray(MAGIC_OFFSET, MAGIC_OFFSET + 6))\n}\n\nfunction isGNU (buf) {\n  return b4a.equals(GNU_MAGIC, buf.subarray(MAGIC_OFFSET, MAGIC_OFFSET + 6)) &&\n    b4a.equals(GNU_VER, buf.subarray(VERSION_OFFSET, VERSION_OFFSET + 2))\n}\n\nfunction clamp (index, len, defaultValue) {\n  if (typeof index !== 'number') return defaultValue\n  index = ~~index // Coerce to integer.\n  if (index >= len) return len\n  if (index >= 0) return index\n  index += len\n  if (index >= 0) return index\n  return 0\n}\n\nfunction toType (flag) {\n  switch (flag) {\n    case 0:\n      return 'file'\n    case 1:\n      return 'link'\n    case 2:\n      return 'symlink'\n    case 3:\n      return 'character-device'\n    case 4:\n      return 'block-device'\n    case 5:\n      return 'directory'\n    case 6:\n      return 'fifo'\n    case 7:\n      return 'contiguous-file'\n    case 72:\n      return 'pax-header'\n    case 55:\n      return 'pax-global-header'\n    case 27:\n      return 'gnu-long-link-path'\n    case 28:\n    case 30:\n      return 'gnu-long-path'\n  }\n\n  return null\n}\n\nfunction toTypeflag (flag) {\n  switch (flag) {\n    case 'file':\n      return 0\n    case 'link':\n      return 1\n    case 'symlink':\n      return 2\n    case 'character-device':\n      return 3\n    case 'block-device':\n      return 4\n    case 'directory':\n      return 5\n    case 'fifo':\n      return 6\n    case 'contiguous-file':\n      return 7\n    case 'pax-header':\n      return 72\n  }\n\n  return 0\n}\n\nfunction indexOf (block, num, offset, end) {\n  for (; offset < end; offset++) {\n    if (block[offset] === num) return offset\n  }\n  return end\n}\n\nfunction cksum (block) {\n  let sum = 8 * 32\n  for (let i = 0; i < 148; i++) sum += block[i]\n  for (let j = 156; j < 512; j++) sum += block[j]\n  return sum\n}\n\nfunction encodeOct (val, n) {\n  val = val.toString(8)\n  if (val.length > n) return SEVENS.slice(0, n) + ' '\n  return ZEROS.slice(0, n - val.length) + val + ' '\n}\n\nfunction encodeSizeBin (num, buf, off) {\n  buf[off] = 0x80\n  for (let i = 11; i > 0; i--) {\n    buf[off + i] = num & 0xff\n    num = Math.floor(num / 0x100)\n  }\n}\n\nfunction encodeSize (num, buf, off) {\n  if (num.toString(8).length > 11) {\n    encodeSizeBin(num, buf, off)\n  } else {\n    b4a.write(buf, encodeOct(num, 11), off)\n  }\n}\n\n/* Copied from the node-tar repo and modified to meet\n * tar-stream coding standard.\n *\n * Source: https://github.com/npm/node-tar/blob/51b6627a1f357d2eb433e7378e5f05e83b7aa6cd/lib/header.js#L349\n */\nfunction parse256 (buf) {\n  // first byte MUST be either 80 or FF\n  // 80 for positive, FF for 2's comp\n  let positive\n  if (buf[0] === 0x80) positive = true\n  else if (buf[0] === 0xFF) positive = false\n  else return null\n\n  // build up a base-256 tuple from the least sig to the highest\n  const tuple = []\n  let i\n  for (i = buf.length - 1; i > 0; i--) {\n    const byte = buf[i]\n    if (positive) tuple.push(byte)\n    else tuple.push(0xFF - byte)\n  }\n\n  let sum = 0\n  const l = tuple.length\n  for (i = 0; i < l; i++) {\n    sum += tuple[i] * Math.pow(256, i)\n  }\n\n  return positive ? sum : -1 * sum\n}\n\nfunction decodeOct (val, offset, length) {\n  val = val.subarray(offset, offset + length)\n  offset = 0\n\n  // If prefixed with 0x80 then parse as a base-256 integer\n  if (val[offset] & 0x80) {\n    return parse256(val)\n  } else {\n    // Older versions of tar can prefix with spaces\n    while (offset < val.length && val[offset] === 32) offset++\n    const end = clamp(indexOf(val, 32, offset, val.length), val.length, val.length)\n    while (offset < end && val[offset] === 0) offset++\n    if (end === offset) return 0\n    return parseInt(b4a.toString(val.subarray(offset, end)), 8)\n  }\n}\n\nfunction decodeStr (val, offset, length, encoding) {\n  return b4a.toString(val.subarray(offset, indexOf(val, 0, offset, offset + length)), encoding)\n}\n\nfunction addLength (str) {\n  const len = b4a.byteLength(str)\n  let digits = Math.floor(Math.log(len) / Math.log(10)) + 1\n  if (len + digits >= Math.pow(10, digits)) digits++\n\n  return (len + digits) + str\n}\n", "const { Writable, Readable, getStreamError } = require('streamx')\nconst FIFO = require('fast-fifo')\nconst b4a = require('b4a')\nconst headers = require('./headers')\n\nconst EMPTY = b4a.alloc(0)\n\nclass BufferList {\n  constructor () {\n    this.buffered = 0\n    this.shifted = 0\n    this.queue = new FIFO()\n\n    this._offset = 0\n  }\n\n  push (buffer) {\n    this.buffered += buffer.byteLength\n    this.queue.push(buffer)\n  }\n\n  shiftFirst (size) {\n    return this._buffered === 0 ? null : this._next(size)\n  }\n\n  shift (size) {\n    if (size > this.buffered) return null\n    if (size === 0) return EMPTY\n\n    let chunk = this._next(size)\n\n    if (size === chunk.byteLength) return chunk // likely case\n\n    const chunks = [chunk]\n\n    while ((size -= chunk.byteLength) > 0) {\n      chunk = this._next(size)\n      chunks.push(chunk)\n    }\n\n    return b4a.concat(chunks)\n  }\n\n  _next (size) {\n    const buf = this.queue.peek()\n    const rem = buf.byteLength - this._offset\n\n    if (size >= rem) {\n      const sub = this._offset ? buf.subarray(this._offset, buf.byteLength) : buf\n      this.queue.shift()\n      this._offset = 0\n      this.buffered -= rem\n      this.shifted += rem\n      return sub\n    }\n\n    this.buffered -= size\n    this.shifted += size\n\n    return buf.subarray(this._offset, (this._offset += size))\n  }\n}\n\nclass Source extends Readable {\n  constructor (self, header, offset) {\n    super()\n\n    this.header = header\n    this.offset = offset\n\n    this._parent = self\n  }\n\n  _read (cb) {\n    if (this.header.size === 0) {\n      this.push(null)\n    }\n    if (this._parent._stream === this) {\n      this._parent._update()\n    }\n    cb(null)\n  }\n\n  _predestroy () {\n    this._parent.destroy(getStreamError(this))\n  }\n\n  _detach () {\n    if (this._parent._stream === this) {\n      this._parent._stream = null\n      this._parent._missing = overflow(this.header.size)\n      this._parent._update()\n    }\n  }\n\n  _destroy (cb) {\n    this._detach()\n    cb(null)\n  }\n}\n\nclass Extract extends Writable {\n  constructor (opts) {\n    super(opts)\n\n    if (!opts) opts = {}\n\n    this._buffer = new BufferList()\n    this._offset = 0\n    this._header = null\n    this._stream = null\n    this._missing = 0\n    this._longHeader = false\n    this._callback = noop\n    this._locked = false\n    this._finished = false\n    this._pax = null\n    this._paxGlobal = null\n    this._gnuLongPath = null\n    this._gnuLongLinkPath = null\n    this._filenameEncoding = opts.filenameEncoding || 'utf-8'\n    this._allowUnknownFormat = !!opts.allowUnknownFormat\n    this._unlockBound = this._unlock.bind(this)\n  }\n\n  _unlock (err) {\n    this._locked = false\n\n    if (err) {\n      this.destroy(err)\n      this._continueWrite(err)\n      return\n    }\n\n    this._update()\n  }\n\n  _consumeHeader () {\n    if (this._locked) return false\n\n    this._offset = this._buffer.shifted\n\n    try {\n      this._header = headers.decode(this._buffer.shift(512), this._filenameEncoding, this._allowUnknownFormat)\n    } catch (err) {\n      this._continueWrite(err)\n      return false\n    }\n\n    if (!this._header) return true\n\n    switch (this._header.type) {\n      case 'gnu-long-path':\n      case 'gnu-long-link-path':\n      case 'pax-global-header':\n      case 'pax-header':\n        this._longHeader = true\n        this._missing = this._header.size\n        return true\n    }\n\n    this._locked = true\n    this._applyLongHeaders()\n\n    if (this._header.size === 0 || this._header.type === 'directory') {\n      this.emit('entry', this._header, this._createStream(), this._unlockBound)\n      return true\n    }\n\n    this._stream = this._createStream()\n    this._missing = this._header.size\n\n    this.emit('entry', this._header, this._stream, this._unlockBound)\n    return true\n  }\n\n  _applyLongHeaders () {\n    if (this._gnuLongPath) {\n      this._header.name = this._gnuLongPath\n      this._gnuLongPath = null\n    }\n\n    if (this._gnuLongLinkPath) {\n      this._header.linkname = this._gnuLongLinkPath\n      this._gnuLongLinkPath = null\n    }\n\n    if (this._pax) {\n      if (this._pax.path) this._header.name = this._pax.path\n      if (this._pax.linkpath) this._header.linkname = this._pax.linkpath\n      if (this._pax.size) this._header.size = parseInt(this._pax.size, 10)\n      this._header.pax = this._pax\n      this._pax = null\n    }\n  }\n\n  _decodeLongHeader (buf) {\n    switch (this._header.type) {\n      case 'gnu-long-path':\n        this._gnuLongPath = headers.decodeLongPath(buf, this._filenameEncoding)\n        break\n      case 'gnu-long-link-path':\n        this._gnuLongLinkPath = headers.decodeLongPath(buf, this._filenameEncoding)\n        break\n      case 'pax-global-header':\n        this._paxGlobal = headers.decodePax(buf)\n        break\n      case 'pax-header':\n        this._pax = this._paxGlobal === null\n          ? headers.decodePax(buf)\n          : Object.assign({}, this._paxGlobal, headers.decodePax(buf))\n        break\n    }\n  }\n\n  _consumeLongHeader () {\n    this._longHeader = false\n    this._missing = overflow(this._header.size)\n\n    const buf = this._buffer.shift(this._header.size)\n\n    try {\n      this._decodeLongHeader(buf)\n    } catch (err) {\n      this._continueWrite(err)\n      return false\n    }\n\n    return true\n  }\n\n  _consumeStream () {\n    const buf = this._buffer.shiftFirst(this._missing)\n    if (buf === null) return false\n\n    this._missing -= buf.byteLength\n    const drained = this._stream.push(buf)\n\n    if (this._missing === 0) {\n      this._stream.push(null)\n      if (drained) this._stream._detach()\n      return drained && this._locked === false\n    }\n\n    return drained\n  }\n\n  _createStream () {\n    return new Source(this, this._header, this._offset)\n  }\n\n  _update () {\n    while (this._buffer.buffered > 0 && !this.destroying) {\n      if (this._missing > 0) {\n        if (this._stream !== null) {\n          if (this._consumeStream() === false) return\n          continue\n        }\n\n        if (this._longHeader === true) {\n          if (this._missing > this._buffer.buffered) break\n          if (this._consumeLongHeader() === false) return false\n          continue\n        }\n\n        const ignore = this._buffer.shiftFirst(this._missing)\n        if (ignore !== null) this._missing -= ignore.byteLength\n        continue\n      }\n\n      if (this._buffer.buffered < 512) break\n      if (this._stream !== null || this._consumeHeader() === false) return\n    }\n\n    this._continueWrite(null)\n  }\n\n  _continueWrite (err) {\n    const cb = this._callback\n    this._callback = noop\n    cb(err)\n  }\n\n  _write (data, cb) {\n    this._callback = cb\n    this._buffer.push(data)\n    this._update()\n  }\n\n  _final (cb) {\n    this._finished = this._missing === 0 && this._buffer.buffered === 0\n    cb(this._finished ? null : new Error('Unexpected end of data'))\n  }\n\n  _predestroy () {\n    this._continueWrite(null)\n  }\n\n  _destroy (cb) {\n    if (this._stream) this._stream.destroy(getStreamError(this))\n    cb(null)\n  }\n\n  [Symbol.asyncIterator] () {\n    let error = null\n\n    let promiseResolve = null\n    let promiseReject = null\n\n    let entryStream = null\n    let entryCallback = null\n\n    const extract = this\n\n    this.on('entry', onentry)\n    this.on('error', (err) => { error = err })\n    this.on('close', onclose)\n\n    return {\n      [Symbol.asyncIterator] () {\n        return this\n      },\n      next () {\n        return new Promise(onnext)\n      },\n      return () {\n        return destroy(null)\n      },\n      throw (err) {\n        return destroy(err)\n      }\n    }\n\n    function consumeCallback (err) {\n      if (!entryCallback) return\n      const cb = entryCallback\n      entryCallback = null\n      cb(err)\n    }\n\n    function onnext (resolve, reject) {\n      if (error) {\n        return reject(error)\n      }\n\n      if (entryStream) {\n        resolve({ value: entryStream, done: false })\n        entryStream = null\n        return\n      }\n\n      promiseResolve = resolve\n      promiseReject = reject\n\n      consumeCallback(null)\n\n      if (extract._finished && promiseResolve) {\n        promiseResolve({ value: undefined, done: true })\n        promiseResolve = promiseReject = null\n      }\n    }\n\n    function onentry (header, stream, callback) {\n      entryCallback = callback\n      stream.on('error', noop) // no way around this due to tick sillyness\n\n      if (promiseResolve) {\n        promiseResolve({ value: stream, done: false })\n        promiseResolve = promiseReject = null\n      } else {\n        entryStream = stream\n      }\n    }\n\n    function onclose () {\n      consumeCallback(error)\n      if (!promiseResolve) return\n      if (error) promiseReject(error)\n      else promiseResolve({ value: undefined, done: true })\n      promiseResolve = promiseReject = null\n    }\n\n    function destroy (err) {\n      extract.destroy(err)\n      consumeCallback(err)\n      return new Promise((resolve, reject) => {\n        if (extract.destroyed) return resolve({ value: undefined, done: true })\n        extract.once('close', function () {\n          if (err) reject(err)\n          else resolve({ value: undefined, done: true })\n        })\n      })\n    }\n  }\n}\n\nmodule.exports = function extract (opts) {\n  return new Extract(opts)\n}\n\nfunction noop () {}\n\nfunction overflow (size) {\n  size &= 511\n  return size && 512 - size\n}\n", "const constants = { // just for envs without fs\n  S_IFMT: 61440,\n  S_IFDIR: 16384,\n  S_IFCHR: 8192,\n  S_IFBLK: 24576,\n  S_IFIFO: 4096,\n  S_IFLNK: 40960\n}\n\ntry {\n  module.exports = require('fs').constants || constants\n} catch {\n  module.exports = constants\n}\n", "const { Readable, Writable, getStreamError } = require('streamx')\nconst b4a = require('b4a')\n\nconst constants = require('./constants')\nconst headers = require('./headers')\n\nconst DMODE = 0o755\nconst FMODE = 0o644\n\nconst END_OF_TAR = b4a.alloc(1024)\n\nclass Sink extends Writable {\n  constructor (pack, header, callback) {\n    super({ mapWritable, eagerOpen: true })\n\n    this.written = 0\n    this.header = header\n\n    this._callback = callback\n    this._linkname = null\n    this._isLinkname = header.type === 'symlink' && !header.linkname\n    this._isVoid = header.type !== 'file' && header.type !== 'contiguous-file'\n    this._finished = false\n    this._pack = pack\n    this._openCallback = null\n\n    if (this._pack._stream === null) this._pack._stream = this\n    else this._pack._pending.push(this)\n  }\n\n  _open (cb) {\n    this._openCallback = cb\n    if (this._pack._stream === this) this._continueOpen()\n  }\n\n  _continuePack (err) {\n    if (this._callback === null) return\n\n    const callback = this._callback\n    this._callback = null\n\n    callback(err)\n  }\n\n  _continueOpen () {\n    if (this._pack._stream === null) this._pack._stream = this\n\n    const cb = this._openCallback\n    this._openCallback = null\n    if (cb === null) return\n\n    if (this._pack.destroying) return cb(new Error('pack stream destroyed'))\n    if (this._pack._finalized) return cb(new Error('pack stream is already finalized'))\n\n    this._pack._stream = this\n\n    if (!this._isLinkname) {\n      this._pack._encode(this.header)\n    }\n\n    if (this._isVoid) {\n      this._finish()\n      this._continuePack(null)\n    }\n\n    cb(null)\n  }\n\n  _write (data, cb) {\n    if (this._isLinkname) {\n      this._linkname = this._linkname ? b4a.concat([this._linkname, data]) : data\n      return cb(null)\n    }\n\n    if (this._isVoid) {\n      if (data.byteLength > 0) {\n        return cb(new Error('No body allowed for this entry'))\n      }\n      return cb()\n    }\n\n    this.written += data.byteLength\n    if (this._pack.push(data)) return cb()\n    this._pack._drain = cb\n  }\n\n  _finish () {\n    if (this._finished) return\n    this._finished = true\n\n    if (this._isLinkname) {\n      this.header.linkname = this._linkname ? b4a.toString(this._linkname, 'utf-8') : ''\n      this._pack._encode(this.header)\n    }\n\n    overflow(this._pack, this.header.size)\n\n    this._pack._done(this)\n  }\n\n  _final (cb) {\n    if (this.written !== this.header.size) { // corrupting tar\n      return cb(new Error('Size mismatch'))\n    }\n\n    this._finish()\n    cb(null)\n  }\n\n  _getError () {\n    return getStreamError(this) || new Error('tar entry destroyed')\n  }\n\n  _predestroy () {\n    this._pack.destroy(this._getError())\n  }\n\n  _destroy (cb) {\n    this._pack._done(this)\n\n    this._continuePack(this._finished ? null : this._getError())\n\n    cb()\n  }\n}\n\nclass Pack extends Readable {\n  constructor (opts) {\n    super(opts)\n    this._drain = noop\n    this._finalized = false\n    this._finalizing = false\n    this._pending = []\n    this._stream = null\n  }\n\n  entry (header, buffer, callback) {\n    if (this._finalized || this.destroying) throw new Error('already finalized or destroyed')\n\n    if (typeof buffer === 'function') {\n      callback = buffer\n      buffer = null\n    }\n\n    if (!callback) callback = noop\n\n    if (!header.size || header.type === 'symlink') header.size = 0\n    if (!header.type) header.type = modeToType(header.mode)\n    if (!header.mode) header.mode = header.type === 'directory' ? DMODE : FMODE\n    if (!header.uid) header.uid = 0\n    if (!header.gid) header.gid = 0\n    if (!header.mtime) header.mtime = new Date()\n\n    if (typeof buffer === 'string') buffer = b4a.from(buffer)\n\n    const sink = new Sink(this, header, callback)\n\n    if (b4a.isBuffer(buffer)) {\n      header.size = buffer.byteLength\n      sink.write(buffer)\n      sink.end()\n      return sink\n    }\n\n    if (sink._isVoid) {\n      return sink\n    }\n\n    return sink\n  }\n\n  finalize () {\n    if (this._stream || this._pending.length > 0) {\n      this._finalizing = true\n      return\n    }\n\n    if (this._finalized) return\n    this._finalized = true\n\n    this.push(END_OF_TAR)\n    this.push(null)\n  }\n\n  _done (stream) {\n    if (stream !== this._stream) return\n\n    this._stream = null\n\n    if (this._finalizing) this.finalize()\n    if (this._pending.length) this._pending.shift()._continueOpen()\n  }\n\n  _encode (header) {\n    if (!header.pax) {\n      const buf = headers.encode(header)\n      if (buf) {\n        this.push(buf)\n        return\n      }\n    }\n    this._encodePax(header)\n  }\n\n  _encodePax (header) {\n    const paxHeader = headers.encodePax({\n      name: header.name,\n      linkname: header.linkname,\n      pax: header.pax\n    })\n\n    const newHeader = {\n      name: 'PaxHeader',\n      mode: header.mode,\n      uid: header.uid,\n      gid: header.gid,\n      size: paxHeader.byteLength,\n      mtime: header.mtime,\n      type: 'pax-header',\n      linkname: header.linkname && 'PaxHeader',\n      uname: header.uname,\n      gname: header.gname,\n      devmajor: header.devmajor,\n      devminor: header.devminor\n    }\n\n    this.push(headers.encode(newHeader))\n    this.push(paxHeader)\n    overflow(this, paxHeader.byteLength)\n\n    newHeader.size = header.size\n    newHeader.type = header.type\n    this.push(headers.encode(newHeader))\n  }\n\n  _doDrain () {\n    const drain = this._drain\n    this._drain = noop\n    drain()\n  }\n\n  _predestroy () {\n    const err = getStreamError(this)\n\n    if (this._stream) this._stream.destroy(err)\n\n    while (this._pending.length) {\n      const stream = this._pending.shift()\n      stream.destroy(err)\n      stream._continueOpen()\n    }\n\n    this._doDrain()\n  }\n\n  _read (cb) {\n    this._doDrain()\n    cb()\n  }\n}\n\nmodule.exports = function pack (opts) {\n  return new Pack(opts)\n}\n\nfunction modeToType (mode) {\n  switch (mode & constants.S_IFMT) {\n    case constants.S_IFBLK: return 'block-device'\n    case constants.S_IFCHR: return 'character-device'\n    case constants.S_IFDIR: return 'directory'\n    case constants.S_IFIFO: return 'fifo'\n    case constants.S_IFLNK: return 'symlink'\n  }\n\n  return 'file'\n}\n\nfunction noop () {}\n\nfunction overflow (self, size) {\n  size &= 511\n  if (size) self.push(END_OF_TAR.subarray(0, 512 - size))\n}\n\nfunction mapWritable (buf) {\n  return b4a.isBuffer(buf) ? buf : b4a.from(buf)\n}\n", "// Returns a wrapper function that returns a wrapped callback\n// The wrapper function should do some stuff, and return a\n// presumably different callback function.\n// This makes sure that own properties are retained, so that\n// decorations and such are not lost along the way.\nmodule.exports = wrappy\nfunction wrappy (fn, cb) {\n  if (fn && cb) return wrappy(fn)(cb)\n\n  if (typeof fn !== 'function')\n    throw new TypeError('need wrapper function')\n\n  Object.keys(fn).forEach(function (k) {\n    wrapper[k] = fn[k]\n  })\n\n  return wrapper\n\n  function wrapper() {\n    var args = new Array(arguments.length)\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i]\n    }\n    var ret = fn.apply(this, args)\n    var cb = args[args.length-1]\n    if (typeof ret === 'function' && ret !== cb) {\n      Object.keys(cb).forEach(function (k) {\n        ret[k] = cb[k]\n      })\n    }\n    return ret\n  }\n}\n", "var wrappy = require('wrappy')\nmodule.exports = wrappy(once)\nmodule.exports.strict = wrappy(onceStrict)\n\nonce.proto = once(function () {\n  Object.defineProperty(Function.prototype, 'once', {\n    value: function () {\n      return once(this)\n    },\n    configurable: true\n  })\n\n  Object.defineProperty(Function.prototype, 'onceStrict', {\n    value: function () {\n      return onceStrict(this)\n    },\n    configurable: true\n  })\n})\n\nfunction once (fn) {\n  var f = function () {\n    if (f.called) return f.value\n    f.called = true\n    return f.value = fn.apply(this, arguments)\n  }\n  f.called = false\n  return f\n}\n\nfunction onceStrict (fn) {\n  var f = function () {\n    if (f.called)\n      throw new Error(f.onceError)\n    f.called = true\n    return f.value = fn.apply(this, arguments)\n  }\n  var name = fn.name || 'Function wrapped with `once`'\n  f.onceError = name + \" shouldn't be called more than once\"\n  f.called = false\n  return f\n}\n", "var once = require('once');\n\nvar noop = function() {};\n\nvar qnt = global.Bare ? queueMicrotask : process.nextTick.bind(process);\n\nvar isRequest = function(stream) {\n\treturn stream.setHeader && typeof stream.abort === 'function';\n};\n\nvar isChildProcess = function(stream) {\n\treturn stream.stdio && Array.isArray(stream.stdio) && stream.stdio.length === 3\n};\n\nvar eos = function(stream, opts, callback) {\n\tif (typeof opts === 'function') return eos(stream, null, opts);\n\tif (!opts) opts = {};\n\n\tcallback = once(callback || noop);\n\n\tvar ws = stream._writableState;\n\tvar rs = stream._readableState;\n\tvar readable = opts.readable || (opts.readable !== false && stream.readable);\n\tvar writable = opts.writable || (opts.writable !== false && stream.writable);\n\tvar cancelled = false;\n\n\tvar onlegacyfinish = function() {\n\t\tif (!stream.writable) onfinish();\n\t};\n\n\tvar onfinish = function() {\n\t\twritable = false;\n\t\tif (!readable) callback.call(stream);\n\t};\n\n\tvar onend = function() {\n\t\treadable = false;\n\t\tif (!writable) callback.call(stream);\n\t};\n\n\tvar onexit = function(exitCode) {\n\t\tcallback.call(stream, exitCode ? new Error('exited with error code: ' + exitCode) : null);\n\t};\n\n\tvar onerror = function(err) {\n\t\tcallback.call(stream, err);\n\t};\n\n\tvar onclose = function() {\n\t\tqnt(onclosenexttick);\n\t};\n\n\tvar onclosenexttick = function() {\n\t\tif (cancelled) return;\n\t\tif (readable && !(rs && (rs.ended && !rs.destroyed))) return callback.call(stream, new Error('premature close'));\n\t\tif (writable && !(ws && (ws.ended && !ws.destroyed))) return callback.call(stream, new Error('premature close'));\n\t};\n\n\tvar onrequest = function() {\n\t\tstream.req.on('finish', onfinish);\n\t};\n\n\tif (isRequest(stream)) {\n\t\tstream.on('complete', onfinish);\n\t\tstream.on('abort', onclose);\n\t\tif (stream.req) onrequest();\n\t\telse stream.on('request', onrequest);\n\t} else if (writable && !ws) { // legacy streams\n\t\tstream.on('end', onlegacyfinish);\n\t\tstream.on('close', onlegacyfinish);\n\t}\n\n\tif (isChildProcess(stream)) stream.on('exit', onexit);\n\n\tstream.on('end', onend);\n\tstream.on('finish', onfinish);\n\tif (opts.error !== false) stream.on('error', onerror);\n\tstream.on('close', onclose);\n\n\treturn function() {\n\t\tcancelled = true;\n\t\tstream.removeListener('complete', onfinish);\n\t\tstream.removeListener('abort', onclose);\n\t\tstream.removeListener('request', onrequest);\n\t\tif (stream.req) stream.req.removeListener('finish', onfinish);\n\t\tstream.removeListener('end', onlegacyfinish);\n\t\tstream.removeListener('close', onlegacyfinish);\n\t\tstream.removeListener('finish', onfinish);\n\t\tstream.removeListener('exit', onexit);\n\t\tstream.removeListener('end', onend);\n\t\tstream.removeListener('error', onerror);\n\t\tstream.removeListener('close', onclose);\n\t};\n};\n\nmodule.exports = eos;\n", "var once = require('once')\nvar eos = require('end-of-stream')\nvar fs\n\ntry {\n  fs = require('fs') // we only need fs to get the ReadStream and WriteStream prototypes\n} catch (e) {}\n\nvar noop = function () {}\nvar ancient = typeof process === 'undefined' ? false : /^v?\\.0/.test(process.version)\n\nvar isFn = function (fn) {\n  return typeof fn === 'function'\n}\n\nvar isFS = function (stream) {\n  if (!ancient) return false // newer node version do not need to care about fs is a special way\n  if (!fs) return false // browser\n  return (stream instanceof (fs.ReadStream || noop) || stream instanceof (fs.WriteStream || noop)) && isFn(stream.close)\n}\n\nvar isRequest = function (stream) {\n  return stream.setHeader && isFn(stream.abort)\n}\n\nvar destroyer = function (stream, reading, writing, callback) {\n  callback = once(callback)\n\n  var closed = false\n  stream.on('close', function () {\n    closed = true\n  })\n\n  eos(stream, {readable: reading, writable: writing}, function (err) {\n    if (err) return callback(err)\n    closed = true\n    callback()\n  })\n\n  var destroyed = false\n  return function (err) {\n    if (closed) return\n    if (destroyed) return\n    destroyed = true\n\n    if (isFS(stream)) return stream.close(noop) // use close for fs streams to avoid fd leaks\n    if (isRequest(stream)) return stream.abort() // request.destroy just do .end - .abort is what we want\n\n    if (isFn(stream.destroy)) return stream.destroy()\n\n    callback(err || new Error('stream was destroyed'))\n  }\n}\n\nvar call = function (fn) {\n  fn()\n}\n\nvar pipe = function (from, to) {\n  return from.pipe(to)\n}\n\nvar pump = function () {\n  var streams = Array.prototype.slice.call(arguments)\n  var callback = isFn(streams[streams.length - 1] || noop) && streams.pop() || noop\n\n  if (Array.isArray(streams[0])) streams = streams[0]\n  if (streams.length < 2) throw new Error('pump requires two streams per minimum')\n\n  var error\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1\n    var writing = i > 0\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err\n      if (err) destroys.forEach(call)\n      if (reading) return\n      destroys.forEach(call)\n      callback(error)\n    })\n  })\n\n  return streams.reduce(pipe)\n}\n\nmodule.exports = pump\n", "const tar = require('tar-stream')\nconst pump = require('pump')\nconst fs = require('fs')\nconst path = require('path')\n\nconst win32 = (global.Bare ? global.Bare.platform : process.platform) === 'win32'\n\nexports.pack = function pack (cwd, opts) {\n  if (!cwd) cwd = '.'\n  if (!opts) opts = {}\n\n  const xfs = opts.fs || fs\n  const ignore = opts.ignore || opts.filter || noop\n  const mapStream = opts.mapStream || echo\n  const statNext = statAll(xfs, opts.dereference ? xfs.stat : xfs.lstat, cwd, ignore, opts.entries, opts.sort)\n  const strict = opts.strict !== false\n  const umask = typeof opts.umask === 'number' ? ~opts.umask : ~processUmask()\n  const pack = opts.pack || tar.pack()\n  const finish = opts.finish || noop\n\n  let map = opts.map || noop\n  let dmode = typeof opts.dmode === 'number' ? opts.dmode : 0\n  let fmode = typeof opts.fmode === 'number' ? opts.fmode : 0\n\n  if (opts.strip) map = strip(map, opts.strip)\n\n  if (opts.readable) {\n    dmode |= parseInt(555, 8)\n    fmode |= parseInt(444, 8)\n  }\n  if (opts.writable) {\n    dmode |= parseInt(333, 8)\n    fmode |= parseInt(222, 8)\n  }\n\n  onnextentry()\n\n  function onsymlink (filename, header) {\n    xfs.readlink(path.join(cwd, filename), function (err, linkname) {\n      if (err) return pack.destroy(err)\n      header.linkname = normalize(linkname)\n      pack.entry(header, onnextentry)\n    })\n  }\n\n  function onstat (err, filename, stat) {\n    if (pack.destroyed) return\n    if (err) return pack.destroy(err)\n    if (!filename) {\n      if (opts.finalize !== false) pack.finalize()\n      return finish(pack)\n    }\n\n    if (stat.isSocket()) return onnextentry() // tar does not support sockets...\n\n    let header = {\n      name: normalize(filename),\n      mode: (stat.mode | (stat.isDirectory() ? dmode : fmode)) & umask,\n      mtime: stat.mtime,\n      size: stat.size,\n      type: 'file',\n      uid: stat.uid,\n      gid: stat.gid\n    }\n\n    if (stat.isDirectory()) {\n      header.size = 0\n      header.type = 'directory'\n      header = map(header) || header\n      return pack.entry(header, onnextentry)\n    }\n\n    if (stat.isSymbolicLink()) {\n      header.size = 0\n      header.type = 'symlink'\n      header = map(header) || header\n      return onsymlink(filename, header)\n    }\n\n    // TODO: add fifo etc...\n\n    header = map(header) || header\n\n    if (!stat.isFile()) {\n      if (strict) return pack.destroy(new Error('unsupported type for ' + filename))\n      return onnextentry()\n    }\n\n    const entry = pack.entry(header, onnextentry)\n    const rs = mapStream(xfs.createReadStream(path.join(cwd, filename), { start: 0, end: header.size > 0 ? header.size - 1 : header.size }), header)\n\n    rs.on('error', function (err) { // always forward errors on destroy\n      entry.destroy(err)\n    })\n\n    pump(rs, entry)\n  }\n\n  function onnextentry (err) {\n    if (err) return pack.destroy(err)\n    statNext(onstat)\n  }\n\n  return pack\n}\n\nfunction head (list) {\n  return list.length ? list[list.length - 1] : null\n}\n\nfunction processGetuid () {\n  return (!global.Bare && process.getuid) ? process.getuid() : -1\n}\n\nfunction processUmask () {\n  return (!global.Bare && process.umask) ? process.umask() : 0\n}\n\nexports.extract = function extract (cwd, opts) {\n  if (!cwd) cwd = '.'\n  if (!opts) opts = {}\n\n  cwd = path.resolve(cwd)\n\n  const xfs = opts.fs || fs\n  const ignore = opts.ignore || opts.filter || noop\n  const mapStream = opts.mapStream || echo\n  const own = opts.chown !== false && !win32 && processGetuid() === 0\n  const extract = opts.extract || tar.extract()\n  const stack = []\n  const now = new Date()\n  const umask = typeof opts.umask === 'number' ? ~opts.umask : ~processUmask()\n  const strict = opts.strict !== false\n  const validateSymLinks = opts.validateSymlinks !== false\n\n  let map = opts.map || noop\n  let dmode = typeof opts.dmode === 'number' ? opts.dmode : 0\n  let fmode = typeof opts.fmode === 'number' ? opts.fmode : 0\n\n  if (opts.strip) map = strip(map, opts.strip)\n\n  if (opts.readable) {\n    dmode |= parseInt(555, 8)\n    fmode |= parseInt(444, 8)\n  }\n  if (opts.writable) {\n    dmode |= parseInt(333, 8)\n    fmode |= parseInt(222, 8)\n  }\n\n  extract.on('entry', onentry)\n\n  if (opts.finish) extract.on('finish', opts.finish)\n\n  return extract\n\n  function onentry (header, stream, next) {\n    header = map(header) || header\n    header.name = normalize(header.name)\n\n    const name = path.join(cwd, path.join('/', header.name))\n\n    if (ignore(name, header)) {\n      stream.resume()\n      return next()\n    }\n\n    const dir = path.join(name, '.') === path.join(cwd, '.') ? cwd : path.dirname(name)\n\n    validate(xfs, dir, path.join(cwd, '.'), function (err, valid) {\n      if (err) return next(err)\n      if (!valid) return next(new Error(dir + ' is not a valid path'))\n\n      if (header.type === 'directory') {\n        stack.push([name, header.mtime])\n        return mkdirfix(name, {\n          fs: xfs,\n          own,\n          uid: header.uid,\n          gid: header.gid,\n          mode: header.mode\n        }, stat)\n      }\n\n      mkdirfix(dir, {\n        fs: xfs,\n        own,\n        uid: header.uid,\n        gid: header.gid,\n        // normally, the folders with rights and owner should be part of the TAR file\n        // if this is not the case, create folder for same user as file and with\n        // standard permissions of 0o755 (rwxr-xr-x)\n        mode: 0o755\n      }, function (err) {\n        if (err) return next(err)\n\n        switch (header.type) {\n          case 'file': return onfile()\n          case 'link': return onlink()\n          case 'symlink': return onsymlink()\n        }\n\n        if (strict) return next(new Error('unsupported type for ' + name + ' (' + header.type + ')'))\n\n        stream.resume()\n        next()\n      })\n    })\n\n    function stat (err) {\n      if (err) return next(err)\n      utimes(name, header, function (err) {\n        if (err) return next(err)\n        if (win32) return next()\n        chperm(name, header, next)\n      })\n    }\n\n    function onsymlink () {\n      if (win32) return next() // skip symlinks on win for now before it can be tested\n      xfs.unlink(name, function () {\n        const dst = path.resolve(path.dirname(name), header.linkname)\n        if (!inCwd(dst) && validateSymLinks) return next(new Error(name + ' is not a valid symlink'))\n\n        xfs.symlink(header.linkname, name, stat)\n      })\n    }\n\n    function onlink () {\n      if (win32) return next() // skip links on win for now before it can be tested\n      xfs.unlink(name, function () {\n        const link = path.join(cwd, path.join('/', header.linkname))\n\n        fs.realpath(link, function (err, dst) {\n          if (err || !inCwd(dst)) return next(new Error(name + ' is not a valid hardlink'))\n\n          xfs.link(dst, name, function (err) {\n            if (err && err.code === 'EPERM' && opts.hardlinkAsFilesFallback) {\n              stream = xfs.createReadStream(dst)\n              return onfile()\n            }\n\n            stat(err)\n          })\n        })\n      })\n    }\n\n    function inCwd (dst) {\n      return dst.startsWith(cwd)\n    }\n\n    function onfile () {\n      const ws = xfs.createWriteStream(name)\n      const rs = mapStream(stream, header)\n\n      ws.on('error', function (err) { // always forward errors on destroy\n        rs.destroy(err)\n      })\n\n      pump(rs, ws, function (err) {\n        if (err) return next(err)\n        ws.on('close', stat)\n      })\n    }\n  }\n\n  function utimesParent (name, cb) { // we just set the mtime on the parent dir again everytime we write an entry\n    let top\n    while ((top = head(stack)) && name.slice(0, top[0].length) !== top[0]) stack.pop()\n    if (!top) return cb()\n    xfs.utimes(top[0], now, top[1], cb)\n  }\n\n  function utimes (name, header, cb) {\n    if (opts.utimes === false) return cb()\n\n    if (header.type === 'directory') return xfs.utimes(name, now, header.mtime, cb)\n    if (header.type === 'symlink') return utimesParent(name, cb) // TODO: how to set mtime on link?\n\n    xfs.utimes(name, now, header.mtime, function (err) {\n      if (err) return cb(err)\n      utimesParent(name, cb)\n    })\n  }\n\n  function chperm (name, header, cb) {\n    const link = header.type === 'symlink'\n\n    /* eslint-disable n/no-deprecated-api */\n    const chmod = link ? xfs.lchmod : xfs.chmod\n    const chown = link ? xfs.lchown : xfs.chown\n    /* eslint-enable n/no-deprecated-api */\n\n    if (!chmod) return cb()\n\n    const mode = (header.mode | (header.type === 'directory' ? dmode : fmode)) & umask\n\n    if (chown && own) chown.call(xfs, name, header.uid, header.gid, onchown)\n    else onchown(null)\n\n    function onchown (err) {\n      if (err) return cb(err)\n      if (!chmod) return cb()\n      chmod.call(xfs, name, mode, cb)\n    }\n  }\n\n  function mkdirfix (name, opts, cb) {\n    // when mkdir is called on an existing directory, the permissions\n    // will be overwritten (?), to avoid this we check for its existance first\n    xfs.stat(name, function (err) {\n      if (!err) return cb(null)\n      if (err.code !== 'ENOENT') return cb(err)\n      xfs.mkdir(name, { mode: opts.mode, recursive: true }, function (err, made) {\n        if (err) return cb(err)\n        chperm(name, opts, cb)\n      })\n    })\n  }\n}\n\nfunction validate (fs, name, root, cb) {\n  if (name === root) return cb(null, true)\n\n  fs.lstat(name, function (err, st) {\n    if (err && err.code !== 'ENOENT' && err.code !== 'EPERM') return cb(err)\n    if (err || st.isDirectory()) return validate(fs, path.join(name, '..'), root, cb)\n    cb(null, false)\n  })\n}\n\nfunction noop () {}\n\nfunction echo (name) {\n  return name\n}\n\nfunction normalize (name) {\n  return win32 ? name.replace(/\\\\/g, '/').replace(/[:?<>|]/g, '_') : name\n}\n\nfunction statAll (fs, stat, cwd, ignore, entries, sort) {\n  if (!entries) entries = ['.']\n  const queue = entries.slice(0)\n\n  return function loop (callback) {\n    if (!queue.length) return callback(null)\n\n    const next = queue.shift()\n    const nextAbs = path.join(cwd, next)\n\n    stat.call(fs, nextAbs, function (err, stat) {\n      // ignore errors if the files were deleted while buffering\n      if (err) return callback(entries.indexOf(next) === -1 && err.code === 'ENOENT' ? null : err)\n\n      if (!stat.isDirectory()) return callback(null, next, stat)\n\n      fs.readdir(nextAbs, function (err, files) {\n        if (err) return callback(err)\n\n        if (sort) files.sort()\n\n        for (let i = 0; i < files.length; i++) {\n          if (!ignore(path.join(cwd, next, files[i]))) queue.push(path.join(next, files[i]))\n        }\n\n        callback(null, next, stat)\n      })\n    })\n  }\n}\n\nfunction strip (map, level) {\n  return function (header) {\n    header.name = header.name.split('/').slice(level).join('/')\n\n    const linkname = header.linkname\n    if (linkname && (header.type === 'link' || path.isAbsolute(linkname))) {\n      header.linkname = linkname.split('/').slice(level).join('/')\n    }\n\n    return map(header)\n  }\n}\n", "import fr from \"follow-redirects\";\nimport { access, createWriteStream, rm, symlink } from \"node:fs\";\nimport { tmpdir } from \"node:os\";\nimport { join } from \"node:path\";\nimport { extract } from \"tar-fs\";\n/**\n * Creates a symlink to a file\n */\nexport const createSymlink = (source, target) => {\n    return new Promise((resolve, reject) => {\n        access(source, (error) => {\n            if (error) {\n                reject(error);\n                return;\n            }\n            symlink(source, target, (error) => {\n                /* c8 ignore next */\n                if (error) {\n                    /* c8 ignore next 3 */\n                    reject(error);\n                    return;\n                }\n                resolve();\n            });\n        });\n    });\n};\n/**\n * Downloads a file from a URL\n */\nexport const downloadFile = (url, outputPath) => {\n    return new Promise((resolve, reject) => {\n        const stream = createWriteStream(outputPath);\n        stream.once(\"error\", reject);\n        fr.https\n            .get(url, (response) => {\n            if (response.statusCode !== 200) {\n                stream.close();\n                reject(new Error(\n                /* c8 ignore next 2 */\n                `Unexpected status code: ${response.statusCode?.toFixed(0) ?? \"UNK\"}.`));\n                return;\n            }\n            // Pipe directly to file rather than manually writing chunks\n            // This is more efficient and uses less memory\n            response.pipe(stream);\n            // Listen for completion\n            stream.once(\"finish\", () => {\n                stream.close();\n                resolve();\n            });\n            // Handle response errors\n            response.once(\"error\", (error) => {\n                /* c8 ignore next 2 */\n                stream.close();\n                reject(error);\n            });\n        })\n            /* c8 ignore next 3 */\n            .on(\"error\", (error) => {\n            stream.close();\n            reject(error);\n        });\n    });\n};\n/**\n * Adds the proper folders to the environment\n * @param baseLibPath the path to this packages lib folder\n */\nexport const setupLambdaEnvironment = (baseLibPath) => {\n    // If the FONTCONFIG_PATH is not set, set it to /tmp/fonts\n    process.env[\"FONTCONFIG_PATH\"] ??= join(tmpdir(), \"fonts\");\n    // Set up Home folder if not already set\n    process.env[\"HOME\"] ??= tmpdir();\n    // If LD_LIBRARY_PATH is undefined, set it to baseLibPath, otherwise, add it\n    if (process.env[\"LD_LIBRARY_PATH\"] === undefined) {\n        process.env[\"LD_LIBRARY_PATH\"] = baseLibPath;\n    }\n    else if (!process.env[\"LD_LIBRARY_PATH\"].startsWith(baseLibPath)) {\n        process.env[\"LD_LIBRARY_PATH\"] = [\n            baseLibPath,\n            ...new Set(process.env[\"LD_LIBRARY_PATH\"].split(\":\")),\n        ].join(\":\");\n    }\n};\n/**\n * Determines if the input is a valid URL\n * @param input the input to check\n * @returns boolean indicating if the input is a valid URL\n */\nexport const isValidUrl = (input) => {\n    try {\n        return Boolean(new URL(input));\n    }\n    catch {\n        return false;\n    }\n};\n/**\n * Determines if the running instance is inside an Amazon Linux 2023 container,\n * AWS_EXECUTION_ENV is for native Lambda instances\n * AWS_LAMBDA_JS_RUNTIME is for netlify instances\n * CODEBUILD_BUILD_IMAGE is for CodeBuild instances\n * VERCEL is for Vercel Functions (Node 20 or later enables an AL2023-compatible environment).\n * @returns boolean indicating if the running instance is inside a Lambda container with nodejs20\n */\nexport const isRunningInAmazonLinux2023 = (nodeMajorVersion) => {\n    const awsExecEnv = process.env[\"AWS_EXECUTION_ENV\"] ?? \"\";\n    const awsLambdaJsRuntime = process.env[\"AWS_LAMBDA_JS_RUNTIME\"] ?? \"\";\n    const codebuildImage = process.env[\"CODEBUILD_BUILD_IMAGE\"] ?? \"\";\n    // Check for explicit version substrings, returns on first match\n    if (awsExecEnv.includes(\"20.x\") ||\n        awsExecEnv.includes(\"22.x\") ||\n        awsLambdaJsRuntime.includes(\"20.x\") ||\n        awsLambdaJsRuntime.includes(\"22.x\") ||\n        codebuildImage.includes(\"nodejs20\") ||\n        codebuildImage.includes(\"nodejs22\")) {\n        return true;\n    }\n    // Vercel: Node 20+ is AL2023 compatible\n    // eslint-disable-next-line sonarjs/prefer-single-boolean-return\n    if (process.env[\"VERCEL\"] && nodeMajorVersion >= 20) {\n        return true;\n    }\n    return false;\n};\nexport const downloadAndExtract = async (url) => {\n    const getOptions = new URL(url);\n    // Increase the max body length to 60MB for larger files\n    getOptions.maxBodyLength = 60 * 1024 * 1024;\n    const destDir = join(tmpdir(), \"chromium-pack\");\n    return new Promise((resolve, reject) => {\n        const extractObj = extract(destDir);\n        // Setup error handlers for better cleanup\n        /* c8 ignore next 5 */\n        const cleanupOnError = (err) => {\n            rm(destDir, { force: true, recursive: true }, () => {\n                reject(err);\n            });\n        };\n        // Attach error handler to extract stream\n        extractObj.once(\"error\", cleanupOnError);\n        // Handle extraction completion\n        extractObj.once(\"finish\", () => {\n            resolve(destDir);\n        });\n        const req = fr.https.get(url, (response) => {\n            /* c8 ignore next */\n            if (response.statusCode !== 200) {\n                /* c8 ignore next 9 */\n                reject(new Error(`Unexpected status code: ${response.statusCode?.toFixed(0) ?? \"UNK\"}.`));\n                return;\n            }\n            // Pipe the response directly to the extraction stream\n            response.pipe(extractObj);\n            // Handle response errors\n            response.once(\"error\", cleanupOnError);\n        });\n        // Handle request errors\n        req.once(\"error\", cleanupOnError);\n        // Set a timeout to avoid hanging requests\n        req.setTimeout(60 * 1000, () => {\n            /* c8 ignore next 2 */\n            req.destroy();\n            cleanupOnError(new Error(\"Request timeout\"));\n        });\n    });\n};\n", "import { createReadStream, createWriteStream, existsSync } from \"node:fs\";\nimport { tmpdir } from \"node:os\";\nimport { basename, join } from \"node:path\";\nimport { createBrotliDecompress, createUnzip } from \"node:zlib\";\nimport { extract } from \"tar-fs\";\n/**\n * Decompresses a (tarballed) Brotli or Gzip compressed file and returns the path to the decompressed file/folder.\n *\n * @param filePath Path of the file to decompress.\n */\nexport const inflate = (filePath) => {\n    // Determine the output path based on the file type\n    const output = filePath.includes(\"swiftshader\")\n        ? tmpdir()\n        : join(tmpdir(), basename(filePath).replace(/\\.(?:t(?:ar(?:\\.(?:br|gz))?|br|gz)|br|gz)$/i, \"\"));\n    return new Promise((resolve, reject) => {\n        // Quick return if the file is already decompressed\n        if (filePath.includes(\"swiftshader\")) {\n            if (existsSync(`${output}/libGLESv2.so`)) {\n                resolve(output);\n                return;\n            }\n        }\n        else if (existsSync(output)) {\n            resolve(output);\n            return;\n        }\n        // Optimize chunk size based on file type - use smaller chunks for better memory usage\n        // Brotli files tend to decompress to much larger sizes\n        const isBrotli = /br$/i.test(filePath);\n        const isGzip = /gz$/i.test(filePath);\n        const isTar = /\\.t(?:ar(?:\\.(?:br|gz))?|br|gz)$/i.test(filePath);\n        // Use a smaller highWaterMark for better memory efficiency\n        // For most serverless environments, 4MB (2**22) is more memory-efficient than 8MB\n        const highWaterMark = 2 ** 22;\n        const source = createReadStream(filePath, { highWaterMark });\n        let target;\n        // Setup error handlers first for both streams\n        const handleError = (error) => {\n            reject(error);\n        };\n        source.once(\"error\", handleError);\n        // Setup the appropriate target stream based on file type\n        if (isTar) {\n            target = extract(output);\n            target.once(\"finish\", () => {\n                resolve(output);\n            });\n        }\n        else {\n            target = createWriteStream(output, { mode: 0o700 });\n            target.once(\"close\", () => {\n                resolve(output);\n            });\n        }\n        target.once(\"error\", handleError);\n        // Pipe through the appropriate decompressor if needed\n        if (isBrotli || isGzip) {\n            // Use optimized chunk size for decompression\n            // 2MB (2**21) is sufficient for most brotli/gzip files\n            const decompressor = isBrotli\n                ? createBrotliDecompress({ chunkSize: 2 ** 21 })\n                : createUnzip({ chunkSize: 2 ** 21 });\n            // Handle decompressor errors\n            decompressor.once(\"error\", handleError);\n            // Chain the streams\n            source.pipe(decompressor).pipe(target);\n        }\n        else {\n            source.pipe(target);\n        }\n    });\n};\n", "import { existsSync, mkdirSync } from \"node:fs\";\nimport { tmpdir } from \"node:os\";\nimport { join } from \"node:path\";\nimport { URL } from \"node:url\";\nimport { createSymlink, downloadAndExtract, downloadFile, isRunningInAmazonLinux2023, isValidUrl, setupLambdaEnvironment, } from \"./helper.js\";\nimport { inflate } from \"./lambdafs.js\";\nimport { getBinPath } from \"./paths.esm.js\";\nconst nodeMajorVersion = Number.parseInt(process.versions.node.split(\".\")[0] ?? \"\");\n// Setup the lambda environment\nif (isRunningInAmazonLinux2023(nodeMajorVersion)) {\n    setupLambdaEnvironment(join(tmpdir(), \"al2023\", \"lib\"));\n}\n// eslint-disable-next-line @typescript-eslint/no-extraneous-class\nclass Chromium {\n    /**\n     * Returns a list of additional Chromium flags recommended for serverless environments.\n     * The canonical list of flags can be found on https://peter.sh/experiments/chromium-command-line-switches/.\n     * Most of below can be found here: https://github.com/GoogleChrome/chrome-launcher/blob/main/docs/chrome-flags-for-tools.md\n     */\n    static get args() {\n        const chromiumFlags = [\n            \"--ash-no-nudges\", // Avoids blue bubble \"user education\" nudges (eg., \"… give your browser a new look\", Memory Saver)\n            \"--disable-domain-reliability\", // Disables Domain Reliability Monitoring, which tracks whether the browser has difficulty contacting Google-owned sites and uploads reports to Google.\n            \"--disable-print-preview\", // https://source.chromium.org/search?q=lang:cpp+symbol:kDisablePrintPreview&ss=chromium\n            \"--disk-cache-size=33554432\", // https://source.chromium.org/search?q=lang:cpp+symbol:kDiskCacheSize&ss=chromium Forces the maximum disk space to be used by the disk cache, in bytes.\n            \"--no-default-browser-check\", // Disable the default browser check, do not prompt to set it as such. (This is already set by Playwright, but not Puppeteer)\n            \"--no-pings\", // Don't send hyperlink auditing pings\n            \"--single-process\", // Runs the renderer and plugins in the same process as the browser. NOTES: Needs to be single-process to avoid `prctl(PR_SET_NO_NEW_PRIVS) failed` error\n            \"--font-render-hinting=none\", // https://github.com/puppeteer/puppeteer/issues/2410#issuecomment-560573612\n        ];\n        const chromiumDisableFeatures = [\n            \"AudioServiceOutOfProcess\",\n            \"IsolateOrigins\",\n            \"site-per-process\", // Disables OOPIF. https://www.chromium.org/Home/chromium-security/site-isolation\n        ];\n        const chromiumEnableFeatures = [\"SharedArrayBuffer\"];\n        const graphicsFlags = [\n            \"--ignore-gpu-blocklist\", // https://source.chromium.org/search?q=lang:cpp+symbol:kIgnoreGpuBlocklist&ss=chromium\n            \"--in-process-gpu\", // Saves some memory by moving GPU process into a browser process thread\n        ];\n        // https://chromium.googlesource.com/chromium/src/+/main/docs/gpu/swiftshader.md\n        if (this.graphics) {\n            graphicsFlags.push(\n            // As the unsafe WebGL fallback, SwANGLE (ANGLE + SwiftShader Vulkan)\n            \"--use-gl=angle\", \"--use-angle=swiftshader\", \"--enable-unsafe-swiftshader\");\n        }\n        else {\n            graphicsFlags.push(\"--disable-webgl\");\n        }\n        const insecureFlags = [\n            \"--allow-running-insecure-content\", // https://source.chromium.org/search?q=lang:cpp+symbol:kAllowRunningInsecureContent&ss=chromium\n            \"--disable-setuid-sandbox\", // Lambda runs as root, so this is required to allow Chromium to run as root\n            \"--disable-site-isolation-trials\", // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableSiteIsolation&ss=chromium\n            \"--disable-web-security\", // https://source.chromium.org/search?q=lang:cpp+symbol:kDisableWebSecurity&ss=chromium\n        ];\n        const headlessFlags = [\n            \"--headless='shell'\", // We only support running chrome-headless-shell\n            \"--no-sandbox\", // https://source.chromium.org/search?q=lang:cpp+symbol:kNoSandbox&ss=chromium\n            \"--no-zygote\", // https://source.chromium.org/search?q=lang:cpp+symbol:kNoZygote&ss=chromium\n        ];\n        return [\n            ...chromiumFlags,\n            `--disable-features=${[...chromiumDisableFeatures].join(\",\")}`,\n            `--enable-features=${[...chromiumEnableFeatures].join(\",\")}`,\n            ...graphicsFlags,\n            ...insecureFlags,\n            ...headlessFlags,\n        ];\n    }\n    /**\n     * Returns whether the graphics stack is enabled or disabled\n     * @returns boolean\n     */\n    static get graphics() {\n        return this.graphicsMode;\n    }\n    /**\n     * Sets whether the graphics stack is enabled or disabled.\n     * @param true means the stack is enabled. WebGL will work.\n     * @param false means that the stack is disabled. WebGL will not work.\n     * @default true\n     */\n    static set setGraphicsMode(value) {\n        if (typeof value !== \"boolean\") {\n            throw new TypeError(`Graphics mode must be a boolean, you entered '${String(value)}'`);\n        }\n        this.graphicsMode = value;\n    }\n    /**\n     * If true, the graphics stack and webgl is enabled,\n     * If false, webgl will be disabled.\n     * (If false, the swiftshader.tar.br file will also not extract)\n     */\n    static graphicsMode = true;\n    /**\n     * Inflates the included version of Chromium\n     * @param input The location of the `bin` folder\n     * @returns The path to the `chromium` binary\n     */\n    static async executablePath(input) {\n        /**\n         * If the `chromium` binary already exists in /tmp/chromium, return it.\n         */\n        if (existsSync(join(tmpdir(), \"chromium\"))) {\n            return join(tmpdir(), \"chromium\");\n        }\n        /**\n         * If input is a valid URL, download and extract the file. It will extract to /tmp/chromium-pack\n         * and executablePath will be recursively called on that location, which will then extract\n         * the brotli files to the correct locations\n         */\n        if (input && isValidUrl(input)) {\n            return this.executablePath(await downloadAndExtract(input));\n        }\n        /**\n         * If input is defined, use that as the location of the brotli files,\n         * otherwise, the default location is ../../bin.\n         * A custom location is needed for workflows that using custom packaging.\n         */\n        input ??= getBinPath();\n        /**\n         * If the input directory doesn't exist, throw an error.\n         */\n        if (!existsSync(input)) {\n            throw new Error(`The input directory \"${input}\" does not exist. Please provide the location of the brotli files.`);\n        }\n        // Extract the required files\n        const promises = [\n            inflate(join(input, \"chromium.br\")),\n            inflate(join(input, \"fonts.tar.br\")),\n            inflate(join(input, \"swiftshader.tar.br\")),\n        ];\n        if (isRunningInAmazonLinux2023(nodeMajorVersion)) {\n            promises.push(inflate(join(input, \"al2023.tar.br\")));\n        }\n        // Await all extractions\n        const result = await Promise.all(promises);\n        // Returns the first result of the promise, which is the location of the `chromium` binary\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return result.shift();\n    }\n    /**\n     * Downloads or symlinks a custom font and returns its basename, patching the environment so that Chromium can find it.\n     */\n    static async font(input) {\n        const fontsDir = process.env[\"FONTCONFIG_PATH\"] ??\n            join(process.env[\"HOME\"] ?? tmpdir(), \".fonts\");\n        // Create fonts directory if it doesn't exist\n        if (!existsSync(fontsDir)) {\n            mkdirSync(fontsDir);\n        }\n        // Convert local path to file URL if needed\n        if (!/^https?:\\/\\//i.test(input)) {\n            input = `file://${input}`;\n        }\n        const url = new URL(input);\n        const fontName = url.pathname.split(\"/\").pop();\n        if (!fontName) {\n            throw new Error(`Invalid font name: ${url.pathname}`);\n        }\n        const outputPath = `${fontsDir}/${fontName}`;\n        // Return font name if it already exists\n        if (existsSync(outputPath)) {\n            return fontName;\n        }\n        // Handle local file\n        if (url.protocol === \"file:\") {\n            try {\n                await createSymlink(url.pathname, outputPath);\n                return fontName;\n            }\n            catch (error) {\n                throw new Error(`Failed to create symlink for font: ${JSON.stringify(error)}`);\n            }\n        }\n        // Handle remote file\n        else {\n            try {\n                await downloadFile(input, outputPath);\n                return fontName;\n            }\n            catch (error) {\n                throw new Error(`Failed to download font: ${JSON.stringify(error)}`);\n            }\n        }\n    }\n}\nexport default Chromium;\n", "import { dirname, join } from \"node:path\";\nimport { fileURLToPath } from \"node:url\";\n/**\n * Get the bin directory path for ESM modules\n */\nexport function getBinPath() {\n    return join(dirname(fileURLToPath(import.meta.url)), \"..\", \"..\", \"bin\");\n}\n", "// eslint-disable-next-line no-unused-vars\r\nimport { IncomingMessage, } from 'http';\r\n\r\n\r\nclass APIError extends Error {\r\n\t/**\r\n\t * Original error.\r\n\t * @type {?Error}\r\n\t * @default null\r\n\t */\r\n\toriginalError = null;\r\n\r\n\t/**\r\n\t * HTTP response.\r\n\t * @type {IncomingMessage}\r\n\t * @default null\r\n\t */\r\n\thttpResponse = null;\r\n\r\n\t/**\r\n\t * Error message.\r\n\t * @param {string} message Message.\r\n\t */\r\n\tconstructor(message = 'Unknown error') {\r\n\t\tsuper(message);\r\n\t}\r\n\r\n\t/**\r\n\t * Absorb error.\r\n\t * @param {Error} [error=null] Original error.\r\n\t * @param {?IncomingMessage} [httpResponse=null] HTTP response.\r\n\t * @returns {APIError}\r\n\t */\r\n\tstatic absorb(error, httpResponse = null) {\r\n\t\treturn Object.assign(new APIError(error.message), {\r\n\t\t\toriginalError: error,\r\n\t\t\thttpResponse,\r\n\t\t});\r\n\t}\r\n}\r\n\r\nexport default APIError;\r\n", "/**\r\n * @module API\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./options\").nHentaiOptions } nHentaiOptions\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./options\").nHentaiHosts } nHentaiHosts\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./options\").httpAgent } httpAgent\r\n */\r\n\r\n/**\r\n * @typedef { import(\"./search\").SearchSortMode } SearchSortMode\r\n */\r\n\r\n// eslint-disable-next-line no-unused-vars\r\nimport http, { IncomingMessage, } from 'http';\r\nimport https from 'https';\r\n\r\nimport chromium from '@sparticuz/chromium';\r\n\r\nimport { version, } from '../package.json';\r\n\r\nimport Book from './book';\r\nimport APIError from './error';\r\nimport Image from './image';\r\nimport processOptions from './options';\r\nimport { Search, } from './search';\r\nimport { Tag, } from './tag';\r\n\r\n\r\n/**\r\n * API arguments\r\n * @typedef {object} APIArgs\r\n * @property {string}   host    API host.\r\n * @property {Function} apiPath API endpoint URL path generator.\r\n */\r\n\r\n/**\r\n * Class used for building URL paths to nHentai API endpoints.\r\n * This class is internal and has only static methods.\r\n * @class\r\n */\r\nclass APIPath {\r\n\t/**\r\n\t * Search by query endpoint.\r\n\t * @param {string}          query     Search query.\r\n\t * @param {?number}         [page=1]  Page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic search(query, page = 1, sort = '') {\r\n\t\treturn `/api/galleries/search?query=${query}&page=${page}${sort ? '&sort=' + sort : ''}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Search by tag endpoint.\r\n\t * @param {number}  tagID    Tag ID.\r\n\t * @param {?number} [page=1] Page ID.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic searchTagged(tagID, page = 1) {\r\n\t\treturn `/api/galleries/tagged?tag_id=${tagID}&page=${page}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Search alike endpoint.\r\n\t * @param {number} bookID Book ID.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic searchAlike(bookID) {\r\n\t\treturn `/api/gallery/${bookID}/related`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book content endpoint.\r\n\t * @param {number} bookID Book ID.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic book(bookID) {\r\n\t\treturn `/api/gallery/${bookID}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book's cover image endpoint.\r\n\t * @param {number} mediaID   Media ID.\r\n\t * @param {string} extension Image extension.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic bookCover(mediaID, extension) {\r\n\t\treturn `/galleries/${mediaID}/cover.${extension}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book's page image endpoint.\r\n\t * @param {number} mediaID   Media ID.\r\n\t * @param {number} page      Page ID.\r\n\t * @param {string} extension Image extension.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic bookPage(mediaID, page, extension) {\r\n\t\treturn `/galleries/${mediaID}/${page}.${extension}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Book's page's thumbnail image endpoint.\r\n\t * @param {number} mediaID   Media ID.\r\n\t * @param {number} page      Page ID.\r\n\t * @param {string} extension Image extension.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic bookThumb(mediaID, page, extension) {\r\n\t\treturn `/galleries/${mediaID}/${page}t.${extension}`;\r\n\t}\r\n\r\n\t/**\r\n\t * Redirect to random book at website.\r\n\t * @returns {string} URL path.\r\n\t */\r\n\tstatic randomBookRedirect() {\r\n\t\treturn '/random/';\r\n\t}\r\n}\r\n\r\n/**\r\n * Class used for interaction with nHentai API.\r\n * @class\r\n */\r\nclass API {\r\n\t/**\r\n\t * API path class\r\n\t * @type {APIPath}\r\n\t * @static\r\n\t * @private\r\n\t */\r\n\tstatic APIPath = APIPath;\r\n\r\n\t/**\r\n\t * Hosts\r\n\t * @type {?nHentaiHosts}\r\n\t */\r\n\thosts;\r\n\r\n\t/**\r\n\t * Prefer HTTPS over HTTP.\r\n\t * @type {?boolean}\r\n\t */\r\n\tssl;\r\n\r\n\t/**\r\n\t * HTTP(S) agent.\r\n\t * @property {?httpAgent}\r\n\t */\r\n\tagent;\r\n\r\n\t/**\r\n\t * Cookies string.\r\n\t * @type {?string}\r\n\t */\r\n\tcookies;\r\n\r\n\t/**\r\n\t * Use Puppeteer with stealth plugin instead of native HTTP requests.\r\n\t * @type {?boolean}\r\n\t */\r\n\tusePuppeteer;\r\n\r\n\t/**\r\n\t * Additional arguments to pass to Puppeteer browser launch.\r\n\t * @type {?string[]}\r\n\t */\r\n\tbrowserArgs;\r\n\r\n\t/**\r\n\t * Custom function to launch Puppeteer browser.\r\n\t * @type {?Function}\r\n\t */\r\n\tpuppeteerLaunch;\r\n\r\n\t/**\r\n\t * Applies provided options on top of defaults.\r\n\t * @param {?nHentaiOptions} [options={}] Options to apply.\r\n\t */\r\n\tconstructor(options = {}) {\r\n\t\tlet params = processOptions(options);\r\n\r\n\t\tObject.assign(this, params);\r\n\t}\r\n\r\n\t/**\r\n\t * Get http(s) module depending on `options.ssl`.\r\n\t * @type {https|http}\r\n\t */\r\n\tget net() {\r\n\t\treturn this.ssl\r\n\t\t\t? https\r\n\t\t\t: http;\r\n\t}\r\n\r\n\t/**\r\n\t * Select a host from an array of hosts using round-robin.\r\n\t * @param {string[]} hosts Array of hosts.\r\n\t * @param {string} [fallback] Fallback host if array is empty.\r\n\t * @returns {string} Selected host.\r\n\t * @private\r\n\t */\r\n\tselectHost(hosts, fallback = 'nhentai.net') {\r\n\t\tif (!Array.isArray(hosts) || hosts.length === 0) {\r\n\t\t\treturn fallback;\r\n\t\t}\r\n\r\n\t\t// Simple round-robin selection based on current time\r\n\t\tconst index = Math.floor(Math.random() * hosts.length);\r\n\t\treturn hosts[index];\r\n\t}\r\n\r\n\t/**\r\n\t * JSON get request.\r\n\t * @param {object} options      HTTP(S) request options.\r\n\t * @param {string} options.host Host.\r\n\t * @param {string} options.path Path.\r\n\t * @returns {Promise<object>} Parsed JSON.\r\n\t */\r\n\trequest(options) {\r\n\t\t// Use Puppeteer if enabled\r\n\t\tif (this.usePuppeteer) {\r\n\t\t\treturn this.requestWithPuppeteer(options);\r\n\t\t}\r\n\r\n\t\t// Use native HTTP requests\r\n\t\tlet {\r\n\t\t\tnet,\r\n\t\t\tagent,\r\n\t\t\tcookies,\r\n\t\t} = this;\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tconst headers = {\r\n\t\t\t\t'User-Agent': `nhentai-api-client/${version} Node.js/${process.versions.node}`,\r\n\t\t\t};\r\n\r\n\t\t\t// Add cookies if provided\r\n\t\t\tif (cookies) {\r\n\t\t\t\theaders.Cookie = cookies;\r\n\t\t\t}\r\n\r\n\t\t\tObject.assign(options, {\r\n\t\t\t\tagent,\r\n\t\t\t\theaders,\r\n\t\t\t});\r\n\r\n\t\t\tnet.get(options, _response => {\r\n\t\t\t\tconst\r\n\t\t\t\t\t/** @type {IncomingMessage}*/\r\n\t\t\t\t\tresponse = _response,\r\n\t\t\t\t\t{ statusCode, } = response,\r\n\t\t\t\t\tcontentType = response.headers['content-type'];\r\n\r\n\t\t\t\tlet error;\r\n\t\t\t\tif (statusCode !== 200)\r\n\t\t\t\t\terror = new Error(`Request failed with status code ${statusCode}`);\r\n\t\t\t\telse if (!(/^application\\/json/).test(contentType))\r\n\t\t\t\t\terror = new Error(`Invalid content-type - expected application/json but received ${contentType}`);\r\n\r\n\t\t\t\tif (error) {\r\n\t\t\t\t\tresponse.resume();\r\n\t\t\t\t\treject(APIError.absorb(error, response));\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tresponse.setEncoding('utf8');\r\n\t\t\t\tlet rawData = '';\r\n\t\t\t\tresponse.on('data', (chunk) => rawData += chunk);\r\n\t\t\t\tresponse.on('end', () => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tresolve(JSON.parse(rawData));\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\treject(APIError.absorb(error, response));\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}).on('error', error => reject(APIError.absorb(error)));\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * JSON get request using Puppeteer with stealth plugin.\r\n\t * @param {object} options      HTTP(S) request options.\r\n\t * @param {string} options.host Host.\r\n\t * @param {string} options.path Path.\r\n\t * @returns {Promise<object>} Parsed JSON.\r\n\t * @private\r\n\t */\r\n\tasync requestWithPuppeteer(options) {\r\n\t\tlet puppeteer, StealthPlugin;\r\n\r\n\t\ttry {\r\n\t\t\t// Dynamic import to avoid requiring puppeteer when not needed\r\n\t\t\tpuppeteer = await import('puppeteer-extra');\r\n\t\t\tStealthPlugin = (await import('puppeteer-extra-plugin-stealth')).default;\r\n\t\t} catch (error) {\r\n\t\t\tthrow new Error('Puppeteer dependencies not found. Please install puppeteer-extra and puppeteer-extra-plugin-stealth: npm install puppeteer-extra puppeteer-extra-plugin-stealth');\r\n\t\t}\r\n\r\n\t\t// Use stealth plugin\r\n\t\tpuppeteer.default.use(StealthPlugin());\r\n\r\n\t\tconst url = `http${this.ssl ? 's' : ''}://${options.host}${options.path}`;\r\n\t\tlet browser;\r\n\r\n\t\ttry {\r\n\t\t\t// Launch browser with custom function or default configuration\r\n\t\t\tif (this.puppeteerLaunch && typeof this.puppeteerLaunch === 'function') {\r\n\t\t\t\t// Use custom launch function\r\n\t\t\t\tbrowser = await this.puppeteerLaunch();\r\n\t\t\t} else {\r\n\t\t\t\t// Use default launch configuration\r\n\t\t\t\tbrowser = await puppeteer.default.launch({\r\n\t\t\t\t\targs          \t: chromium.args.concat(this.browserArgs || []),\r\n\t\t\t\t\tdefaultViewport: chromium.defaultViewport,\r\n\t\t\t\t\texecutablePath : await chromium.executablePath(),\r\n\t\t\t\t\theadless       : chromium.headless,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tconst page = await browser.newPage();\r\n\r\n\t\t\t// Set user agent\r\n\t\t\tawait page.setUserAgent(`nhentai-api-client/${version} Node.js/${process.versions.node}`);\r\n\r\n\t\t\t// Set cookies if provided\r\n\t\t\tif (this.cookies) {\r\n\t\t\t\tconst cookieStrings = this.cookies.split(';'),\r\n\t\t\t\t\tcookies = cookieStrings.map(cookieStr => {\r\n\t\t\t\t\t\tconst [ name, value, ] = cookieStr.trim().split('=');\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tname  : name.trim(),\r\n\t\t\t\t\t\t\tvalue : value ? value.trim() : '',\r\n\t\t\t\t\t\t\tdomain: options.host,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t});\r\n\t\t\t\tawait page.setCookie(...cookies);\r\n\t\t\t}\r\n\r\n\t\t\t// Check if this is a redirect endpoint (like /random/)\r\n\t\t\tconst isRedirectEndpoint = options.path.includes('/random');\r\n\r\n\t\t\tif (isRedirectEndpoint) {\r\n\t\t\t\t// For redirect endpoints, we need to intercept the redirect response\r\n\t\t\t\tlet redirectResponse = null;\r\n\r\n\t\t\t\tpage.on('response', response => {\r\n\t\t\t\t\tif (response.status() === 302 && response.url() === url) {\r\n\t\t\t\t\t\tredirectResponse = response;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Navigate without following redirects\r\n\t\t\t\tawait page.setRequestInterception(true);\r\n\t\t\t\tpage.on('request', request => {\r\n\t\t\t\t\trequest.continue();\r\n\t\t\t\t});\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait page.goto(url, {\r\n\t\t\t\t\t\twaitUntil: 'networkidle0',\r\n\t\t\t\t\t\ttimeout  : 30000,\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// Expected for redirect endpoints\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (redirectResponse) {\r\n\t\t\t\t\t// Simulate the error that the traditional method expects\r\n\t\t\t\t\tconst mockError = new Error(`Request failed with status code ${redirectResponse.status()}`);\r\n\t\t\t\t\tmockError.httpResponse = {\r\n\t\t\t\t\t\tstatusCode: redirectResponse.status(),\r\n\t\t\t\t\t\theaders   : {\r\n\t\t\t\t\t\t\tlocation: redirectResponse.headers().location,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthrow APIError.absorb(mockError, mockError.httpResponse);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error('Expected redirect response not found');\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// Set request headers to get JSON response for API endpoints\r\n\t\t\t\tawait page.setExtraHTTPHeaders({\r\n\t\t\t\t\t'Accept'      : 'application/json, text/plain, */*',\r\n\t\t\t\t\t'Content-Type': 'application/json',\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Navigate to the URL and get the response\r\n\t\t\t\tconst response = await page.goto(url, {\r\n\t\t\t\t\twaitUntil: 'networkidle0',\r\n\t\t\t\t\ttimeout  : 30000,\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (!response.ok()) {\r\n\t\t\t\t\tthrow new Error(`Request failed with status code ${response.status()}`);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Get the response text directly from the response\r\n\t\t\t\tconst responseText = await response.text(),\r\n\t\t\t\t\t// Check if the response is JSON by looking at content-type or trying to parse\r\n\t\t\t\t\tcontentType = response.headers()['content-type'] || '';\r\n\r\n\t\t\t\tif (contentType.includes('application/json')) {\r\n\t\t\t\t\t// Direct JSON response\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\treturn JSON.parse(responseText);\r\n\t\t\t\t\t} catch (parseError) {\r\n\t\t\t\t\t\tthrow new Error(`Invalid JSON response: ${parseError.message}`);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// HTML response - try to extract JSON from page content\r\n\t\t\t\t\tconst content = await page.content(),\r\n\t\t\t\t\t\tjsonMatch = content.match(/<pre[^>]*>(.*?)<\\/pre>/s);\r\n\t\t\t\t\tlet jsonText;\r\n\r\n\t\t\t\t\tif (jsonMatch) {\r\n\t\t\t\t\t\t// Extract JSON from <pre> tag (common for API responses)\r\n\t\t\t\t\t\tjsonText = jsonMatch[1].trim();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Try to get JSON from page.evaluate\r\n\t\t\t\t\t\tjsonText = await page.evaluate(() => {\r\n\t\t\t\t\t\t\t// Try to find JSON in the page\r\n\t\t\t\t\t\t\t// eslint-disable-next-line no-undef\r\n\t\t\t\t\t\t\tconst preElement = document.querySelector('pre');\r\n\t\t\t\t\t\t\tif (preElement) {\r\n\t\t\t\t\t\t\t\treturn preElement.textContent;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// If no pre element, return the whole body text\r\n\t\t\t\t\t\t\t// eslint-disable-next-line no-undef\r\n\t\t\t\t\t\t\treturn document.body.textContent;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\treturn JSON.parse(jsonText);\r\n\t\t\t\t\t} catch (parseError) {\r\n\t\t\t\t\t\tthrow new Error(`Invalid JSON response: ${parseError.message}. Response content: ${jsonText?.substring(0, 200)}...`);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t} finally {\r\n\t\t\tif (browser) {\r\n\t\t\t\tawait browser.close();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Get API arguments.\r\n\t * This is internal method.\r\n\t * @param {string} hostType Host type.\r\n\t * @param {string} api      Endpoint type.\r\n\t * @returns {APIArgs} API arguments.\r\n\t * @private\r\n\t */\r\n\tgetAPIArgs(hostType, api) {\r\n\t\tlet {\r\n\t\t\thosts: {\r\n\t\t\t\t[hostType]: hostConfig,\r\n\t\t\t},\r\n\t\t\tconstructor: {\r\n\t\t\t\tAPIPath: {\r\n\t\t\t\t\t[api]: apiPath,\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t} = this;\r\n\r\n\t\t// Select host from array or use single host\r\n\t\tconst host = Array.isArray(hostConfig)\r\n\t\t\t? this.selectHost(hostConfig, hostConfig[0])\r\n\t\t\t: hostConfig;\r\n\r\n\t\treturn {\r\n\t\t\thost,\r\n\t\t\tapiPath,\r\n\t\t};\r\n\t}\r\n\r\n\t/**\r\n\t * Search by query.\r\n\t * @param {string}          query     Query.\r\n\t * @param {?number}         [page=1]  Page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @returns {Promise<Search>} Search instance.\r\n\t * @async\r\n\t */\r\n\tasync search(query, page = 1, sort = '') {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'search'),\r\n\t\t\tsearch = Search.parse(\r\n\t\t\t\tawait this.request({\r\n\t\t\t\t\thost,\r\n\t\t\t\t\tpath: apiPath(query, page, sort),\r\n\t\t\t\t})\r\n\t\t\t);\r\n\r\n\t\tObject.assign(search, {\r\n\t\t\tapi: this,\r\n\t\t\tquery,\r\n\t\t\tpage,\r\n\t\t\tsort,\r\n\t\t});\r\n\r\n\t\treturn search;\r\n\t}\r\n\r\n\t/**\r\n\t * Search by query.\r\n\t * @param {string}          query     Query.\r\n\t * @param {?number}         [page=1]  Starting page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @yields {Search} Search instance.\r\n\t * @async\r\n\t * @returns {AsyncGenerator<Search, Search, Search>}\r\n\t */\r\n\tasync * searchGenerator(query, page = 1, sort = '') {\r\n\t\tlet search = await this.search(query, page, sort);\r\n\r\n\t\twhile (search.page <= search.pages) {\r\n\t\t\tyield search;\r\n\t\t\tsearch = await this.search(query, search.page + 1, sort);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Search related books.\r\n\t * @param {number|Book} book Book instance or Book ID.\r\n\t * @returns {Promise<Search>} Search instance.\r\n\t * @async\r\n\t */\r\n\tasync searchAlike(book) {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'searchAlike');\r\n\r\n\t\treturn Search.parse(\r\n\t\t\tawait this.request({\r\n\t\t\t\thost,\r\n\t\t\t\tpath: apiPath(\r\n\t\t\t\t\tbook instanceof Book\r\n\t\t\t\t\t\t? book.id\r\n\t\t\t\t\t\t: +book\r\n\t\t\t\t),\r\n\t\t\t})\r\n\t\t);\r\n\t}\r\n\r\n\t/**\r\n\t * Search by tag id.\r\n\t * @param {number|Tag}      tag       Tag or Tag ID.\r\n\t * @param {?number}         [page=1]  Page ID.\r\n\t * @param {?SearchSortMode} [sort=''] Search sort mode.\r\n\t * @returns {Promise<Search>} Search instance.\r\n\t * @async\r\n\t */\r\n\tasync searchTagged(tag, page = 1, sort = '') {\r\n\t\tif (!(tag instanceof Tag))\r\n\t\t\ttag = Tag.get({ id: +tag, });\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'searchTagged'),\r\n\t\t\tsearch = Search.parse(\r\n\t\t\t\tawait this.request({\r\n\t\t\t\t\thost,\r\n\t\t\t\t\tpath: apiPath(tag.id, page, sort),\r\n\t\t\t\t})\r\n\t\t\t);\r\n\r\n\t\tObject.assign(search, {\r\n\t\t\tapi  : this,\r\n\t\t\tquery: tag,\r\n\t\t\tpage,\r\n\t\t\tsort,\r\n\t\t});\r\n\r\n\t\treturn search;\r\n\t}\r\n\r\n\t/**\r\n\t * Get book by id.\r\n\t * @param {number} bookID Book ID.\r\n\t * @returns {Promise<Book>} Book instance.\r\n\t * @async\r\n\t */\r\n\tasync getBook(bookID) {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'book');\r\n\r\n\t\treturn Book.parse(\r\n\t\t\tawait this.request({\r\n\t\t\t\thost,\r\n\t\t\t\tpath: apiPath(bookID),\r\n\t\t\t})\r\n\t\t);\r\n\t}\r\n\r\n\t/**\r\n\t * Get random book.\r\n\t * @returns {Promise<Book>} Book instance.\r\n\t * @async\r\n\t */\r\n\tasync getRandomBook() {\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('api', 'randomBookRedirect');\r\n\r\n\t\ttry {\r\n\t\t\tawait this.request({\r\n\t\t\t\thost,\r\n\t\t\t\tpath: apiPath(),\r\n\t\t\t}); // Will always throw\r\n\t\t} catch (error) {\r\n\t\t\tif (!(error instanceof APIError))\r\n\t\t\t\tthrow error;\r\n\t\t\tconst response = error.httpResponse;\r\n\t\t\tif (!response || response.statusCode !== 302)\r\n\t\t\t\tthrow error;\r\n\t\t\tconst id = +((/\\d+/).exec(response.headers.location) || {})[0];\r\n\t\t\tif (isNaN(id))\r\n\t\t\t\tthrow APIError.absorb(new Error('Bad redirect'), response);\r\n\t\t\treturn await this.getBook(id);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Detect the actual cover filename extension for nhentai's double extension format.\r\n\t * @param {Image} image Cover image.\r\n\t * @returns {string} The actual extension to use in the URL.\r\n\t * @private\r\n\t */\r\n\tdetectCoverExtension(image) {\r\n\t\tconst reportedExtension = image.type.extension;\r\n\r\n\t\t// Handle WebP cases - both simple and double extension formats\r\n\t\tif (reportedExtension === 'webp') {\r\n\t\t\t// Some WebP files also have double extensions like cover.webp.webp\r\n\t\t\t// We need to detect this based on media ID or other patterns\r\n\r\n\t\t\t// For now, we'll try the double WebP format for certain media ID ranges\r\n\t\t\t// This is based on observation that newer uploads tend to have cover.webp.webp\r\n\t\t\tconst mediaId = image.book.media;\r\n\r\n\t\t\t// Media IDs above ~3000000 seem to use cover.webp.webp format\r\n\t\t\t// This is a heuristic that may need adjustment based on more data\r\n\t\t\tif (mediaId > 3000000) {\r\n\t\t\t\treturn 'webp.webp';\r\n\t\t\t}\r\n\r\n\t\t\t// Default to simple webp for older uploads\r\n\t\t\treturn 'webp';\r\n\t\t}\r\n\r\n\t\t// For non-webp extensions, nhentai often serves double extensions\r\n\t\t// The pattern is: cover.{original_extension}.webp\r\n\t\t// We need to detect what the original extension should be\r\n\r\n\t\t// Map API type codes to likely intermediate extensions\r\n\t\tconst intermediateExtensionMap = {\r\n\t\t\t\t'jpg' : 'jpg',    // API reports 'j' -> likely cover.jpg.webp\r\n\t\t\t\t'jpeg': 'jpg',    // API reports 'jpeg' -> likely cover.jpg.webp\r\n\t\t\t\t'png' : 'png',    // API reports 'p' -> likely cover.png.webp\r\n\t\t\t\t'gif' : 'gif',    // API reports 'g' -> likely cover.gif.webp\r\n\t\t\t},\r\n\t\t\tintermediateExt = intermediateExtensionMap[reportedExtension];\r\n\r\n\t\tif (intermediateExt) {\r\n\t\t\t// Return double extension format: original.webp\r\n\t\t\treturn `${intermediateExt}.webp`;\r\n\t\t}\r\n\r\n\t\t// Fallback to reported extension if we can't map it\r\n\t\treturn reportedExtension;\r\n\t}\r\n\r\n\t/**\r\n\t * Get image URL.\r\n\t * @param {Image} image Image.\r\n\t * @returns {string} Image URL.\r\n\t */\r\n\tgetImageURL(image) {\r\n\t\tif (image instanceof Image) {\r\n\t\t\tlet { host, apiPath, } = image.isCover\r\n\t\t\t\t\t? this.getAPIArgs('thumbs', 'bookCover')\r\n\t\t\t\t\t: this.getAPIArgs('images', 'bookPage'),\r\n\t\t\t\textension;\r\n\r\n\t\t\t// Handle cover images with potential double extensions\r\n\t\t\tif (image.isCover) {\r\n\t\t\t\textension = this.detectCoverExtension(image);\r\n\t\t\t} else {\r\n\t\t\t\t// Regular pages use simple extensions\r\n\t\t\t\textension = image.type.extension;\r\n\t\t\t}\r\n\r\n\t\t\treturn `http${this.ssl ? 's' : ''}://${host}` + (image.isCover\r\n\t\t\t\t? apiPath(image.book.media, extension)\r\n\t\t\t\t: apiPath(image.book.media, image.id, extension));\r\n\t\t}\r\n\t\tthrow new Error('image must be Image instance.');\r\n\t}\r\n\r\n\t/**\r\n\t * Get image URL with original extension (fallback for when double extension fails).\r\n\t * @param {Image} image Image.\r\n\t * @returns {string} Image URL with original extension.\r\n\t */\r\n\tgetImageURLOriginal(image) {\r\n\t\tif (image instanceof Image) {\r\n\t\t\tlet { host, apiPath, } = image.isCover\r\n\t\t\t\t? this.getAPIArgs('thumbs', 'bookCover')\r\n\t\t\t\t: this.getAPIArgs('images', 'bookPage');\r\n\r\n\t\t\t// Always use the original extension reported by the API\r\n\t\t\treturn `http${this.ssl ? 's' : ''}://${host}` + (image.isCover\r\n\t\t\t\t? apiPath(image.book.media, image.type.extension)\r\n\t\t\t\t: apiPath(image.book.media, image.id, image.type.extension));\r\n\t\t}\r\n\t\tthrow new Error('image must be Image instance.');\r\n\t}\r\n\r\n\t/**\r\n\t * Get all possible cover image URL variants for testing.\r\n\t * @param {Image} image Cover image.\r\n\t * @returns {string[]} Array of possible URLs to try.\r\n\t */\r\n\tgetCoverURLVariants(image) {\r\n\t\tif (!(image instanceof Image) || !image.isCover) {\r\n\t\t\tthrow new Error('image must be a cover Image instance.');\r\n\t\t}\r\n\r\n\t\tlet { host, apiPath, } = this.getAPIArgs('thumbs', 'bookCover'),\r\n\t\t\tbaseURL = `http${this.ssl ? 's' : ''}://${host}`,\r\n\t\t\treportedExt = image.type.extension,\r\n\t\t\tvariants = [],\r\n\t\t\t// Add the smart detection URL (our primary method)\r\n\t\t\tsmartExt = this.detectCoverExtension(image);\r\n\r\n\t\tvariants.push(baseURL + apiPath(image.book.media, smartExt));\r\n\r\n\t\t// Add original extension URL\r\n\t\tvariants.push(baseURL + apiPath(image.book.media, reportedExt));\r\n\r\n\t\t// For WebP, add both simple and double variants\r\n\t\tif (reportedExt === 'webp') {\r\n\t\t\tvariants.push(baseURL + apiPath(image.book.media, 'webp'));\r\n\t\t\tvariants.push(baseURL + apiPath(image.book.media, 'webp.webp'));\r\n\t\t}\r\n\r\n\t\t// For non-WebP, add the double extension variant\r\n\t\tif (reportedExt !== 'webp') {\r\n\t\t\tvariants.push(baseURL + apiPath(image.book.media, `${reportedExt}.webp`));\r\n\t\t}\r\n\r\n\t\t// Remove duplicates\r\n\t\treturn [ ...new Set(variants), ];\r\n\t}\r\n\r\n\t/**\r\n\t * Get image thumbnail URL.\r\n\t * @param {Image} image Image.\r\n\t * @returns {string} Image thumbnail URL.\r\n\t */\r\n\tgetThumbURL(image) {\r\n\t\tif (image instanceof Image && !image.isCover) {\r\n\t\t\tlet { host, apiPath, } = this.getAPIArgs('thumbs', 'bookThumb');\r\n\r\n\t\t\treturn `http${this.ssl ? 's' : ''}://${host}`\r\n\t\t\t\t+ apiPath(image.book.media, image.id, image.type.extension);\r\n\t\t}\r\n\t\tthrow new Error('image must be Image instance and not book cover.');\r\n\t}\r\n}\r\n\r\nexport default API;\r\n", "import { Agent, } from 'http';\r\nimport { Agent as SSLAgent, } from 'https';\r\n\r\n\r\n/**\r\n * Agent-like object or Agent class or it's instance.\r\n * @global\r\n * @typedef {object|Agent|SSLAgent} httpAgent\r\n */\r\n\r\n/**\r\n * Common nHentai API hosts object.\r\n * @global\r\n * @typedef {object} nHentaiHosts\r\n * @property {?string}         api    Main API host.\r\n * @property {?string|string[]} images Media API host(s). Can be a single host or array of hosts for load balancing.\r\n * @property {?string|string[]} thumbs Media thumbnails API host(s). Can be a single host or array of hosts for load balancing.\r\n */\r\n\r\n/**\r\n * Common nHentai options object.\r\n * @global\r\n * @typedef {object} nHentaiOptions\r\n * @property {?nHentaiHosts} hosts         Hosts.\r\n * @property {?boolean}      ssl           Prefer HTTPS over HTTP.\r\n * @property {?httpAgent}    agent         HTTP(S) agent.\r\n * @property {?string}       cookies       Cookies string in format 'cookie1=value1;cookie2=value2;...'\r\n * @property {?boolean}      usePuppeteer  Use Puppeteer with stealth plugin instead of native HTTP requests.\r\n * @property {?string[]}     browserArgs   Additional arguments to pass to Puppeteer browser launch.\r\n * @property {?Function}     puppeteerLaunch Custom function to launch Puppeteer browser. If provided, this will be used instead of the default launch configuration.\r\n */\r\n\r\n/**\r\n * Applies provided options on top of defaults.\r\n * @param {?nHentaiOptions} [options={}] Options to apply.\r\n * @returns {nHentaiOptions} Unified options.\r\n */\r\nfunction processOptions({\r\n\thosts: {\r\n\t\tapi    = 'nhentai.net',\r\n\t\timages = [\r\n\t\t\t'i1.nhentai.net',\r\n\t\t\t'i2.nhentai.net',\r\n\t\t\t'i3.nhentai.net',\r\n\t\t],\r\n\t\tthumbs = [\r\n\t\t\t't1.nhentai.net',\r\n\t\t\t't2.nhentai.net',\r\n\t\t\t't3.nhentai.net',\r\n\t\t],\r\n\t} = {},\r\n\tssl             = true,\r\n\tagent           = null,\r\n\tcookies         = null,\r\n\tusePuppeteer    = false,\r\n\tbrowserArgs     = [],\r\n\tpuppeteerLaunch = null,\r\n} = {}) {\r\n\tif (!agent)\r\n\t\tagent = ssl\r\n\t\t\t? SSLAgent\r\n\t\t\t: Agent;\r\n\r\n\tif (agent.constructor.name === 'Function')\r\n\t\tagent = new agent();\r\n\r\n\t// Normalize hosts to arrays for consistent handling\r\n\tconst normalizeHosts = (hostConfig) => {\r\n\t\tif (typeof hostConfig === 'string') {\r\n\t\t\treturn [ hostConfig, ];\r\n\t\t}\r\n\t\treturn Array.isArray(hostConfig) ? hostConfig : [ hostConfig, ];\r\n\t};\r\n\r\n\treturn {\r\n\t\thosts: {\r\n\t\t\tapi,\r\n\t\t\timages: normalizeHosts(images),\r\n\t\t\tthumbs: normalizeHosts(thumbs),\r\n\t\t},\r\n\t\tssl,\r\n\t\tagent,\r\n\t\tcookies,\r\n\t\tusePuppeteer,\r\n\t\tbrowserArgs,\r\n\t\tpuppeteerLaunch,\r\n\t};\r\n}\r\n\r\nexport default processOptions;\r\n", "import API from './api';\r\nimport Book from './book';\r\nimport Image from './image';\r\nimport { Search, SearchSort, } from './search';\r\nimport { Tag, } from './tag';\r\n\r\n\r\nexport {\r\n\tAPI,\r\n\tSearch,\r\n\tSearchSort,\r\n\tBook,\r\n\tImage,\r\n\tTag,\r\n};\r\n\r\n/**\r\n * @typedef { import(\"./tag\").TagTypes } TagTypes\r\n */\r\n\r\n/**\r\n * @type {TagTypes}\r\n */\r\nexport const TagTypes = {\r\n\tUnknown  : Tag.types.Unknown,\r\n\tTag      : Tag.types.Tag,\r\n\tCategory : Tag.types.Category,\r\n\tArtist   : Tag.types.Artist,\r\n\tParody   : Tag.types.Parody,\r\n\tCharacter: Tag.types.Character,\r\n\tGroup    : Tag.types.Group,\r\n\tLanguage : Tag.types.Language,\r\n};\r\n"], "names": ["TagType", "constructor", "type", "knownTypes", "this", "isKnown", "UnknownTagType", "toString", "Tag", "tag", "id", "name", "count", "url", "types", "Unknown", "Object", "assign", "get", "compare", "strict", "map", "prop", "reduce", "accum", "current", "includeCount", "Category", "Artist", "<PERSON><PERSON><PERSON>", "Character", "Group", "Language", "known", "toLowerCase", "ImageType", "extension", "UnknownImageType", "Image", "image", "t", "w", "width", "h", "height", "JPEG", "book", "Book", "isCover", "filename", "PNG", "GIF", "WEBP", "TagsArray", "Array", "args", "toNames", "from", "title", "media", "media_id", "favorites", "num_favorites", "scanlator", "uploaded", "Date", "upload_date", "tags", "cover", "parse", "images", "pages", "english", "japanese", "pretty", "setCover", "isArray", "for<PERSON>ach", "pushPage", "bind", "pushTag", "UnknownBook", "page", "push", "hasTag", "some", "elem", "hasTagWith", "getTagsWith", "filter", "pureTags", "TagTypes", "categories", "artists", "parodies", "characters", "groups", "languages", "SearchSort", "Search", "search", "num_pages", "perPage", "per_page", "result", "length", "books", "query", "sort", "pushBook", "getNextPage", "api", "Error", "API", "searchTagged", "debug", "require$$0", "URL", "http", "require$$1", "https", "require$$2", "Writable", "require$$3", "assert", "require$$4", "require", "error", "apply", "arguments", "detectUnsupportedEnvironment", "looksLikeNode", "process", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "document", "looksLikeV8", "isFunction", "captureStackTrace", "console", "warn", "useNativeURL", "code", "preserved<PERSON>rl<PERSON><PERSON>s", "events", "eventHandlers", "create", "event", "arg1", "arg2", "arg3", "_redirectable", "emit", "InvalidUrlError", "createErrorType", "TypeError", "RedirectionError", "TooManyRedirectsError", "MaxBodyLengthExceededError", "WriteAfterEndError", "destroy", "prototype", "noop", "RedirectableRequest", "options", "responseCallback", "call", "_sanitizeOptions", "_options", "_ended", "_ending", "_redirectCount", "_redirects", "_requestBodyLength", "_requestBodyBuffers", "on", "self", "_onNativeResponse", "response", "_processResponse", "cause", "_performRequest", "wrap", "protocols", "exports", "maxRedirects", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeProtocols", "keys", "scheme", "protocol", "nativeProtocol", "wrappedProtocol", "defineProperties", "request", "value", "input", "callback", "isURL", "isString", "spreadUrlObject", "parseUrl", "validateUrl", "host", "hostname", "equal", "configurable", "enumerable", "writable", "wrappedRequest", "end", "parsed", "test", "href", "urlObject", "target", "spread", "key", "startsWith", "slice", "port", "Number", "path", "pathname", "removeMatchingHeaders", "regex", "headers", "lastValue", "header", "undefined", "String", "trim", "message", "baseClass", "CustomError", "properties", "destroyRequest", "removeListener", "abort", "_currentRequest", "write", "data", "encoding", "<PERSON><PERSON><PERSON><PERSON>", "currentRequest", "<PERSON><PERSON><PERSON><PERSON>", "removeHeader", "setTimeout", "msecs", "destroyOnTimeout", "socket", "addListener", "startTimer", "_timeout", "clearTimeout", "clearTimer", "once", "method", "a", "b", "property", "defineProperty", "searchPos", "indexOf", "substring", "agents", "agent", "_currentUrl", "format", "_isRedirect", "i", "buffers", "writeNext", "buffer", "finished", "statusCode", "trackRedirects", "requestHeaders", "location", "followRedirects", "responseUrl", "redirects", "beforeRedirect", "Host", "req", "<PERSON><PERSON><PERSON><PERSON>", "currentHostHeader", "currentUrlParts", "currentHost", "currentUrl", "redirectUrl", "resolveUrl", "relative", "base", "resolve", "isSubdomain", "subdomain", "domain", "dot", "endsWith", "responseDetails", "requestDetails", "followRedirectsModule", "FixedFIFO", "hwm", "mask", "top", "btm", "next", "clear", "fill", "shift", "last", "peek", "isEmpty", "fastFifo", "FastFIFO", "head", "tail", "val", "prev", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "byteOffset", "byteLength", "b4a", "Uint8Array", "isEncoding", "alloc", "size", "allocUnsafe", "allocUnsafeSlow", "string", "concat", "totalLength", "copy", "source", "targetStart", "start", "equals", "offset", "encodingOrOffset", "includes", "byfeOffset", "lastIndexOf", "swap16", "swap32", "swap64", "writeDoubleLE", "writeFloatLE", "writeUInt32LE", "writeInt32LE", "readDoubleLE", "readFloatLE", "readUInt32LE", "readInt32LE", "writeDoubleBE", "writeFloatBE", "writeUInt32BE", "writeInt32BE", "readDoubleBE", "readFloatBE", "readUInt32BE", "readInt32BE", "PassThroughDecoder", "remaining", "decode", "flush", "UTF8Decoder", "codePoint", "bytesSeen", "bytesNeeded", "lowerBoundary", "upperBoundary", "isBoundary", "Math", "max", "n", "byte", "fromCodePoint", "fromCharCode", "EventEmitter", "STREAM_DESTROYED", "PREMATURE_CLOSE", "FIFO", "TextDecoder", "normalizeEncoding", "decoder", "qmt", "queueMicrotask", "fn", "global", "nextTick", "asyncIterator", "Symbol", "WritableState", "stream", "highWaterMark", "mapWritable", "byteLengthWritable", "queue", "buffered", "pipeline", "drains", "defaultByteLength", "afterWrite", "afterUpdateNextTick", "updateWriteNT", "ended", "_duplexState", "WRITE_FINISHING", "WRITE_QUEUED", "MAX", "autoBatch", "cb", "OPEN_STATUS", "_writableState", "DESTROY_STATUS", "_writev", "update", "WRITE_ACTIVE", "_write", "WRITE_PRIMARY", "updateNonPrimary", "continueUpdate", "_final", "afterFinal", "DESTROYING", "READ_ACTIVE", "_open", "afterOpen", "WRITE_NEXT_TICK", "_destroy", "after<PERSON><PERSON><PERSON>", "updateCallback", "WRITE_UPDATING", "updateNextTick", "ReadableState", "mapReadable", "byteLengthReadable", "readAhead", "pipeTo", "afterRead", "updateReadNT", "pipe", "Pipeline", "isStreamx", "onerror", "done", "onclose", "afterDrain", "unshift", "pending", "read", "drain", "READ_RESUMED", "_read", "READ_EMIT_READABLE", "READ_PRIMARY", "READ_DONE", "READ_UPDATING", "updateNextTickIfOpen", "READ_NEXT_TICK", "TransformState", "afterTransform", "src", "dst", "to", "after<PERSON>ipe", "pipeToFinished", "err", "rs", "_readableState", "ws", "tickDrains", "writes", "newListener", "Stream", "opts", "super", "open", "predestroy", "_predestroy", "signal", "addEventListener", "readable", "destroyed", "destroying", "WRITE_NON_PRIMARY", "Readable", "OPENING", "eagerOpen", "setEncoding", "dec", "echo", "mapOrSkip", "dest", "resume", "pause", "static", "ite", "then", "catch", "return", "isReadStreamx", "_fromAsyncIterator", "promiseResolve", "promiseReject", "onreadable", "ondata", "Promise", "reject", "throw", "writev", "final", "cork", "uncork", "batch", "WRITE_UNDRAINED", "state", "isWritev", "s", "Duplex", "min", "Transform", "_transformState", "transform", "_transform", "_flush", "transformAfterFlush", "streams", "all", "pop", "<PERSON><PERSON><PERSON><PERSON>", "fin", "autoDestroy", "rd", "wr", "isStream", "isTypedArray", "streamx", "pipelinePromise", "isEnded", "isFinished", "isDisturbed", "getStreamError", "PassThrough", "ZERO_OFFSET", "charCodeAt", "USTAR_MAGIC", "USTAR_VER", "GNU_MAGIC", "GNU_VER", "block", "num", "cksum", "sum", "j", "encodeOct", "decodeOct", "subarray", "parse256", "buf", "positive", "tuple", "l", "pow", "clamp", "index", "len", "defaultValue", "parseInt", "decodeStr", "add<PERSON><PERSON><PERSON>", "str", "digits", "floor", "log", "decodeLongPath", "encodePax", "linkname", "pax", "decodePax", "keyIndex", "encode", "prefix", "typeflag", "mode", "uid", "gid", "encodeSize", "off", "encodeSizeBin", "mtime", "getTime", "toTypeflag", "flag", "uname", "gname", "dev<PERSON><PERSON><PERSON>", "dev<PERSON><PERSON>", "filenameEncoding", "allowUnknownFormat", "toType", "c", "isUSTAR", "MAGIC_OFFSET", "isGNU", "VERSION_OFFSET", "EMPTY", "BufferList", "shifted", "_offset", "shiftFirst", "_buffered", "_next", "chunk", "chunks", "rem", "sub", "Source", "_parent", "_stream", "_update", "_detach", "_missing", "overflow", "Extract", "_buffer", "_header", "_longHeader", "_callback", "_locked", "_finished", "_pax", "_paxGlobal", "_gnuLongPath", "_gnuLongLinkPath", "_filenameEncoding", "_allowUnknownFormat", "_unlockBound", "_unlock", "_continueWrite", "_consumeHeader", "_applyLongHeaders", "_createStream", "linkpath", "_decodeLongHeader", "_consumeLongHeader", "_consumeStream", "drained", "ignore", "entryStream", "entryCallback", "extract", "onentry", "consumeCallback", "onnext", "constants", "S_IFMT", "S_IFDIR", "S_IFCHR", "S_IFBLK", "S_IFIFO", "S_IFLNK", "constantsModule", "END_OF_TAR", "Sink", "pack", "written", "_linkname", "_isLinkname", "_isVoid", "_pack", "_openCallback", "_pending", "_continueOpen", "_continuePack", "_finalized", "_encode", "_finish", "_drain", "_done", "_getError", "Pack", "_finalizing", "entry", "modeToType", "sink", "finalize", "_encodePax", "p<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_doDrain", "wrappy_1", "wrappy", "k", "wrapper", "ret", "f", "called", "onceStrict", "onceError", "onceModule", "proto", "Function", "fs", "qnt", "<PERSON><PERSON>", "eos", "cancelled", "onlegacyfinish", "onfinish", "onend", "onexit", "exitCode", "onclosenexttick", "onrequest", "isRequest", "stdio", "isChildProcess", "endOfStream", "e", "ancient", "version", "isFn", "destroyer", "reading", "writing", "closed", "ReadStream", "WriteStream", "close", "isFS", "tar", "pump", "destroys", "win32", "platform", "cwd", "xfs", "mapStream", "own", "chown", "processGetuid", "getuid", "stack", "now", "umask", "processUmask", "validateSymLinks", "validateSymlinks", "dmode", "fmode", "strip", "level", "split", "join", "isAbsolute", "normalize", "replace", "dir", "dirname", "stat", "utimes", "utimesParent", "chperm", "inCwd", "onfile", "createWriteStream", "validate", "valid", "mkdirfix", "onlink", "unlink", "link", "realpath", "hardlinkAsFilesFallback", "createReadStream", "onsymlink", "symlink", "finish", "list", "chmod", "lchmod", "lchown", "onchown", "mkdir", "recursive", "made", "root", "lstat", "st", "isDirectory", "isRunningInAmazonLinux2023", "nodeMajorVersion", "awsExecEnv", "env", "awsLambdaJsRuntime", "codebuildImage", "inflate", "filePath", "output", "tmpdir", "basename", "existsSync", "isBrotli", "isGzip", "isTar", "handleError", "decompressor", "createBrotliDecompress", "chunkSize", "createUnzip", "versions", "node", "baseLibPath", "Set", "Chromium", "graphicsFlags", "graphics", "graphicsMode", "setGraphicsMode", "Boolean", "isValidUrl", "executablePath", "async", "destDir", "extractObj", "cleanupOnError", "rm", "force", "fr", "toFixed", "downloadAndExtract", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileURLToPath", "promises", "fontsDir", "mkdirSync", "fontName", "outputPath", "access", "JSON", "stringify", "downloadFile", "APIError", "httpResponse", "originalError", "params", "processOptions", "hosts", "thumbs", "ssl", "cookies", "usePuppeteer", "browserArgs", "puppeteerLaunch", "SSLAgent", "Agent", "normalizeHosts", "hostConfig", "net", "selectHost", "fallback", "random", "requestWithPuppeteer", "<PERSON><PERSON>", "_response", "contentType", "absorb", "rawData", "puppeteer", "StealthPlugin", "default", "use", "browser", "launch", "chromium", "defaultViewport", "headless", "newPage", "setUserAgent", "cookieStr", "<PERSON><PERSON><PERSON><PERSON>", "redirectResponse", "status", "setRequestInterception", "continue", "goto", "waitUntil", "timeout", "mockError", "setExtraHTTPHeaders", "ok", "responseText", "text", "parseError", "jsonMatch", "content", "match", "jsonText", "evaluate", "preElement", "querySelector", "textContent", "body", "getAPIArgs", "hostType", "APIPath", "<PERSON><PERSON><PERSON><PERSON>", "bookID", "exec", "isNaN", "getBook", "detectCoverExtension", "reportedExtension", "intermediateExt", "getImageURL", "getImageURLOriginal", "getCoverURLVariants", "baseURL", "reportedExt", "variants", "smartExt", "getThumbURL", "tagID", "mediaID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BA,MAAMA;;;;;;;;;;;;;;;AAmBLC,YAAYC,kCANL,MAOFA,YACEA,KAAOA,UACPD,YAAYE,WAAWD,MAAQE;;;;KAQlCC,sBACMD,gBAAgBE;;;;KAO1BC,kBACQH,KAAKF;;;;;mBAvCRF,qBAMe,IA0CrB,MAAMM,uBAAuBN;;;;;AAK5BC,YAAYC,KAAO,iBACZ,WACDA,KAAOA;;;;GAQd,MAAMM;;;;;;;;;;;;WA2CMC,YACJA,eAAeL,OACpBK,IAAM,IAAIL,KAAK,CACdM,IAAQD,IAAIC,GACZR,KAAOO,IAAIP,KACXS,KAAOF,IAAIE,KACXC,OAAQH,IAAIG,MACZC,IAAOJ,IAAII,OAENJ;;;;;;;;;;;;;;KA+CRR,aAAYS,GACXA,GAAQ,EADGR,KAEXA,KAAQE,KAAKH,YAAYa,MAAMC,QAFpBJ,KAGXA,KAAQ,GAHGC,MAIXA,MAAQ,EAJGC,IAKXA,IAAQ,IACL,8BA7CC,+BAOET,KAAKH,YAAYa,MAAMC,qCAOvB,iCAOC,8BAOF,IAkBLC,OAAOC,OAAOb,KAAM,CACnBM,GAAAA,GACAR,KAAMA,gBAAgBF,QACnBE,KACAE,KAAKH,YAAYa,MAAMI,IAAIhB,MAC9BS,KAAAA,KACAC,MAAAA,MACAC,IAAAA;;;;;;;;KAYFM,QAAQV,IAAKW,QAAS,MACrBX,IAAML,KAAKH,YAAYiB,IAAIT,KACZ,QAAXW,OACHA,QAAS,OACL,GAAIhB,KAAKM,KAAOD,IAAIC,GACxB,OAAO,UAEC,CACR,KACA,OACA,OACA,QACA,OACCW,KACDC,MAAQb,IAAIa,QAAUlB,KAAKkB,QAC1BC,QACD,CAACC,MAAOC,UAAYL,OACjBI,MAAQC,QACRD,MAAQC;;;;;KASblB,SAASmB,cAAe,UAChBtB,KAAKO,MAAQe,aAChB,KAAItB,KAAKQ,SACV,qBA3JCJ,YAMU,CACdO,QAAW,IAAIT;;AACfE,IAAW,IAAIR,QAAQ,OACvB2B,SAAW,IAAI3B,QAAQ,YACvB4B,OAAW,IAAI5B,QAAQ,UACvB6B,OAAW,IAAI7B,QAAQ,UACvB8B,UAAW,IAAI9B,QAAQ,aACvB+B,MAAW,IAAI/B,QAAQ,SACvBgC,SAAW,IAAIhC,QAAQ;;;;;AAMvBiC,MAAOjC,QAAQG;;;;;;AAOfe,IAAIhB,UACC+B,YACA,iBAAoB/B,OACvBA,KAAOA,KAAKgC,gBACJD,MAAQ7B,KAAK6B,MAAM/B,OACzB+B,MACA,IAAI3B,eAAeJ;;;;;;;;;;;;;;;;;;;ACrGzB,MAAMiC;;;;;;;;;;;;;;;;;;;;;AA2BLlC,YAAYC,KAAMkC,uCAdX,uCAOK,MAQPlC,YACEA,KAAOA,UACPD,YAAYE,WAAWD,MAAQE,WAEhCgC,UAAYA;;;;KAOd/B,sBACMD,gBAAgBiC;;;;KAOtB1B,kBACIP,KAAKF;;;;;mBAhDRiC,uBAMe,IAmDrB,MAAME,yBAAyBF;;;;;;AAM9BlC,YAAYC,KAAMkC,iBACX,KAAMA,gBACPlC,KAAOA;;;;GAQd,MAAMoC;;;;;;;;;;;;;aA8DQC,MAAO7B,GAAK,OAEvB8B,EAAGtC,KACHuC,EAAGC,MACHC,EAAGC,QACAL,aAEG,IAAInC,KAAK,CACfF,KAAAA,KACAwC,OAASA,MACTE,QAASA,OACTlC,GAAAA;;;;;;;;;;;;;;KAgDFT,aAAYS,GACXA,GAAS,EADEgC,MAEXA,MAAS,EAFEE,OAGXA,OAAS,EAHE1C,KAIXA,KAASE,KAAKH,YAAYa,MAAM+B,KAJrBC,KAKXA,KAASC,KAAKhC,SACX,8BA7CC,gCAOG,iCAOC,+BAOFX,KAAKH,YAAYa,MAAM+B,kCAOvBE,KAAKhC,SAkBXC,OAAOC,OAAOb,KAAM,CACnBM,GAAI,iBAAoBA,GACrBA,GAAK,EAAI,EAAIA,GACb,EACHgC,MAAAA,MACAE,OAAAA,OACA1C,KAAMA,gBAAgBiC,UACnBjC,KACAE,KAAKH,YAAYa,MAAMI,IAAIhB,MAC9B4C,KAAMA,gBAAgBC,KACnBD,KACAC,KAAKhC;;;;KAQNiC,qBACI5C,KAAKM,GAAK;;;;KAOduC,qBACK,GAAE7C,KAAK4C,QAAU,QAAU5C,KAAKM,MAAMN,KAAKF,KAAKkC,6BA5JpDE,cAMU,CACdO,KAAM,IAAIV,UAAU,OAAQ,OAC5Be,IAAM,IAAIf,UAAU,MAAO,OAC3BgB,IAAM,IAAIhB,UAAU,MAAO,OAC3BiB,KAAM,IAAIjB,UAAU,OAAQ,QAE5BpB,QAAS,IAAIsB,iBAAiB,UAAW;;;;;AAMzCJ,MAAOE,UAAUhC;;;;;;AAOjBe,IAAIhB,UACC+B,SACA,iBAAoB/B,YACvBA,KAAOA,KAAKgC,mBAEN,QACA,UACA,OACJhC,KAAO,iBAEH,QACA,MACJA,KAAO,gBAEH,QACA,MACJA,KAAO,gBAEH,QACA,OACJA,KAAO,cAID+B,MAAQ7B,KAAK6B,MAAM/B,OACzB+B,MACA,IAAII,iBAAiBnC;;;;;;;AC7I3B,MAAMmD,kBAAkBC,MACvBrD,eAAesD,eACLA;;;;;KAQVC,QAAQ9B,cAAe,UACf4B,MAAMG,KAAKrD,MAAMK,KAAOA,IAAIF,SAASmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GCsB9C,MAAMqB;;;;;;;;;;;;;;;;;aAqBQD,aACL,IAAI1C,KAAK,CACfsD,MAAWZ,KAAKY,MAChBhD,IAAYoC,KAAKpC,GACjBiD,OAAYb,KAAKc,SACjBC,WAAYf,KAAKgB,cACjBC,UAAWjB,KAAKiB,UAChBC,SAAW,IAAIC,KAAyB,KAAnBnB,KAAKoB,aAC1BC,KAAWd,UAAUI,KAAKX,KAAKqB,MAAM1D,KAAOD,IAAIU,IAAIT,OACpD2D,MAAW9B,MAAM+B,MAAMvB,KAAKwB,OAAOF,OACnCG,MAAWzB,KAAKwB,OAAOC,MAAMlD,KAC5B,CAACkB,MAAO7B,KAAO4B,MAAM+B,MAAM9B,QAAS7B;;;;;;;;;;;;;;;;;KAmFvCT,aAAYyD,MACXA,MAAY,CACXc,QAAU,GACVC,SAAU,GACVC,OAAU,IAJAhE,GAMXA,GAAY,EANDiD,MAOXA,MAAY,EAPDE,UAQXA,UAAY,EARDE,UASXA,UAAY,GATDC,SAUXA,SAAY,IAAIC,KAAK,GAVVE,KAWXA,KAAY,IAAId,UAXLe,MAYXA,MAAY,IAAI9B,MAAM,CAAE5B,GAAI,EAAGoC,KAAM1C,OAZ1BmE,MAaXA,MAAY,IACT,iCAxFI,CACPC,QAAU,GACVC,SAAU,GACVC,OAAU,+BAQN,gCAOG,oCAOI,oCAOA,oCAOD,IAAIT,KAAK,gCAOb,IAAIZ,wCAMH,IAAIf,MAAM,CAAE5B,GAAI,EAAGoC,KAAM1C,qCAOzB,SA8BFuE,SAASP,OAEVd,MAAMsB,QAAQL,QACjBA,MAAMM,QAAQzE,KAAK0E,SAASC,KAAK3E,OAE9BkD,MAAMsB,QAAQT,OACjBA,KAAKU,QAAQzE,KAAK4E,QAAQD,KAAK3E,OAEhCY,OAAOC,OAAOb,KAAM,CACnBsD,MAAAA,MACAhD,GAAAA,GACAiD,MAAAA,MACAE,UAAAA,UACAE,UAAAA,UACAC,SAAAA;;;;KAQE3D,sBACMD,gBAAgB6E;;;;;;KAS1BN,SAASP,cACJA,iBAAiB9B,QACpB8B,MAAMtB,KAAO1C,UACRgE,MAAQA,OACN;;;;;;KAWTU,SAASI,aACJA,gBAAgB5C,QACnB4C,KAAKpC,KAAO1C,UACPmE,MAAMY,KAAKD,OACT;;;;;;KAWTF,QAAQvE,YACPA,IAAMD,IAAIU,IAAIT,MAETL,KAAKgF,OAAO3E,YACX0D,KAAKgB,KAAK1E,MACR;;;;;KAUT2E,OAAO3E,IAAKW,QAAS,UACpBX,IAAMD,IAAIU,IAAIT,KAEPL,KAAK+D,KAAKkB,MAAKC,MAAQA,KAAKnE,QAAQV,IAAKW;;;;KAOjDmE,WAAW9E,YACHL,KAAKgF,OAAO3E,IAAK;;;;;KAQzB+E,YAAY/E,YACXA,IAAMD,IAAIU,IAAIT,KAEPL,KAAK+D,KAAKsB,QAAOH,MAAQA,KAAKnE,QAAQV,IAAK;;;;KAO/CiF,sBACItF,KAAKoF,YAAY,CAAEtF,KAAMyF,SAASnF;;;;KAOtCoF,wBACIxF,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAShE;;;;KAOtCkE,qBACIzF,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS/D;;;;KAOtCkE,sBACI1F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS9D;;;;KAOtCkE,wBACI3F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS7D;;;;KAOtCkE,oBACI5F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS5D;;;;KAOtCkE,uBACI7F,KAAKoF,YAAY,CAAEtF,KAAMyF,SAAS3D;;;;;mBA7RrCe,uCAAAA,2BAsSN,MAAMkC,oBAAoBlC;;;;AAIzB9C,oBACO,KAIR8C,KAAKkC,YAAcA,YACnBlC,KAAKhC,QAAU,IAAIkE;;;;;;;;;;;;ACnUnB,MAAMiB;;;;mBAAAA,oBAIW,oBAJXA,qBAQY,2BARZA,0BAYiB,iCAZjBA,yBAgBgB,gCAhBhBA,0BAoBiB,gBAOvB,MAAMC;;;;;aAKQC,eACL,IAAIhG,KAAK,CACfmE,MAAO6B,OAAOC,WACVD,OAAOC,UACR,EACHC,QAASF,OAAOG,UACZH,OAAOG,SACRH,OAAOI,OAAOC,OACjBC,MAAON,OAAOI,OAAOnF,IAAI0B,KAAKsB,MAAMU,KAAKhC;;;;;;;;;;;;;;;KA+D3C9C,aAAY0G,MACXA,MAAU,KADCC,KAEXA,KAAU,GAFC1B,KAGXA,KAAU,EAHCX,MAIXA,MAAU,EAJC+B,QAKXA,QAAU,EALCI,MAMXA,MAAU,IACP,+BA7DE,mCAOE,kCAOD,gCAOA,kCAOG,gCAOF,iCAOA,GAoBHpD,MAAMsB,QAAQ8B,QACjBA,MAAM7B,QAAQzE,KAAKyG,SAAS9B,KAAK3E,OAElCY,OAAOC,OAAOb,KAAM,CACnBuG,MAAAA,MACAC,KAAAA,KACA1B,KAAAA,KACAX,MAAAA,MACA+B,QAAAA;;;;;;KAUFO,SAAS/D,aACJA,gBAAgBC,YACd2D,MAAMvB,KAAKrC,OACT;;;;;;;KAYTgE,YAAYC,IAAM3G,KAAK2G,SAClBJ,MAAEA,MAAFzB,KAASA,KAAT0B,KAAeA,MAAUxG,QACf,OAAVuG,MACH,MAAMK,MAAM,+BACPD,eAAeE,KACpB,MAAMD,MAAM,2BACNL,iBAAiBnG,IACrBuG,IAAIG,aAAaP,MAAOzB,KAAO,EAAG0B,MAClCG,IAAIX,OAAOO,MAAOzB,KAAO,EAAG0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OC/K7BO,mMCAAtG,IAAMuG,4BACNC,MAAMxG,IAAIwG,IACVC,KAAOC,4BACPC,MAAQC,4BACRC,WAAWC,4BAAkBD,SAC7BE,OAASC,4BACTV,MDJa,WACf,IAAKA,QAAO,CACV;;AAEEA,QAAQW,QAAQ,QAARA,CAAiB,oBAE3B,MAAOC,QACc,mBAAVZ,UACTA,QAAQ,cAGZA,QAAMa,MAAM,KAAMC;;;CCHnB,SAASC,+BACR,IAAIC,cAAmC,oBAAZC,QACvBC,iBAAqC,oBAAXC,QAA8C,oBAAbC,SAC3DC,YAAcC,WAAWzB,MAAM0B,mBAC9BP,gBAAkBE,kBAAqBG,aAC1CG,QAAQC,KAAK,wEALjB;;AAUA,IAAIC,cAAe,EACnB,IACEjB,OAAO,IAAIP,MAAI,KAEjB,MAAOU,OACLc,aAA8B,oBAAfd,MAAMe;4CAIvB;IAAIC,mBAAqB,CACvB,OACA,OACA,WACA,OACA,OACA,WACA,OACA,WACA,QACA,SACA,QAIEC,OAAS,CAAC,QAAS,UAAW,UAAW,QAAS,SAAU,WAC5DC,cAAgBjI,OAAOkI,OAAO;wDAClCF;OAAOnE,SAAQ,SAAUsE,OACvBF,cAAcE,OAAS,SAAUC,KAAMC,KAAMC,MAC3ClJ,KAAKmJ,cAAcC,KAAKL,MAAOC,KAAMC,KAAMC;;AAK/C,IAAIG,gBAAkBC,gBACpB,kBACA,cACAC,WAEEC,iBAAmBF,gBACrB,6BACA,6BAEEG,sBAAwBH,gBAC1B,4BACA,uCACAE,kBAEEE,2BAA6BJ,gBAC/B,kCACA,gDAEEK,mBAAqBL,gBACvB,6BACA,mBAIEM,QAAUtC,WAASuC,UAAUD,SAAWE;;AAG5C,SAASC,oBAAoBC,QAASC;;AAEpC3C,WAAS4C,KAAKlK,MACdA,KAAKmK,iBAAiBH,SACtBhK,KAAKoK,SAAWJ,QAChBhK,KAAKqK,QAAS,EACdrK,KAAKsK,SAAU,EACftK,KAAKuK,eAAiB,EACtBvK,KAAKwK,WAAa,GAClBxK,KAAKyK,mBAAqB,EAC1BzK,KAAK0K,oBAAsB;;AAGvBT,kBACFjK,KAAK2K,GAAG,WAAYV;;AAItB,IAAIW,KAAO5K,KACXA,KAAK6K,kBAAoB,SAAUC,UACjC,IACEF,KAAKG,iBAAiBD,UAExB,MAAOE,OACLJ,KAAKxB,KAAK,QAAS4B,iBAAiBxB,iBAClCwB,MAAQ,IAAIxB,iBAAiB,CAAEwB,MAAOA;;AAK5ChL,KAAKiL;;AAmYP,SAASC,KAAKC;;AAEZ,IAAIC,QAAU,CACZC,aAAc,GACdC,cAAe,UAIbC,gBAAkB;qBAqDtB;OApDA3K,OAAO4K,KAAKL,WAAW1G,SAAQ,SAAUgH,QACvC,IAAIC,SAAWD,OAAS,IACpBE,eAAiBJ,gBAAgBG,UAAYP,UAAUM,QACvDG,gBAAkBR,QAAQK,QAAU7K,OAAOkI,OAAO6C;;AA4CtD/K,OAAOiL,iBAAiBD,gBAAiB,CACvCE,QAAS,CAAEC;;AA1Cb,SAASD,QAAQE,MAAOhC,QAASiC;;AA8B/B,OAyIN,SAASC,MAAMH,OACb,OAAO9E,OAAO8E,iBAAiB9E;UAtKvBiF;CAAMF,OAGDG,SAASH,OAChBA,MAAQI,gBAAgBC,SAASL,SAGjCC,SAAWjC,QACXA,QAAUsC,YAAYN,OACtBA,MAAQ,CAAEN,SAAUA,WARpBM,MAAQI,gBAAgBJ,OAUtB3D,WAAW2B,WACbiC,SAAWjC,QACXA,QAAU;;AAIZA,QAAUpJ,OAAOC,OAAO,CACtBwK,aAAcD,QAAQC,aACtBC,cAAeF,QAAQE,eACtBU,MAAOhC,UACFuB,gBAAkBA,gBACrBY,SAASnC,QAAQuC,OAAUJ,SAASnC,QAAQwC,YAC/CxC,QAAQwC,SAAW,OAGrBhF,OAAOiF,MAAMzC,QAAQ0B,SAAUA,SAAU,qBACzC3E,MAAM,UAAWiD,SACV,IAAID,oBAAoBC,QAASiC;;CAYbS,cAAc,EAAMC,YAAY,EAAMC,UAAU,GAC3E9L,IAAK,CAAEiL,MATT,SAASjL,IAAIkL,MAAOhC,QAASiC,UAC3B,IAAIY,eAAiBjB,gBAAgBE,QAAQE,MAAOhC,QAASiC,UAE7D,OADAY,eAAeC,MACRD,gBAMYH,cAAc,EAAMC,YAAY,EAAMC,UAAU,QAGhExB,QAGT,SAAStB,qBAET,SAASuC,SAASL,OAChB,IAAIe;uBAEJ;GAAItE,aACFsE,OAAS,IAAI9F,MAAI+E,YAKjB,IAAKG;;AADLY,OAAST,YAAY7L,IAAIwD,MAAM+H,SACVN,UACnB,MAAM,IAAIrC,gBAAgB,CAAE2C,MAAAA,QAGhC,OAAOe,OAQT,SAAST,YAAYN,OACnB,GAAI,MAAMgB,KAAKhB,MAAMQ,YAAc,oBAAoBQ,KAAKhB,MAAMQ,UAChE,MAAM,IAAInD,gBAAgB,CAAE2C,MAAOA,MAAMiB,MAAQjB,QAEnD,GAAI,MAAMgB,KAAKhB,MAAMO,QAAU,2BAA2BS,KAAKhB,MAAMO,MACnE,MAAM,IAAIlD,gBAAgB,CAAE2C,MAAOA,MAAMiB,MAAQjB,QAEnD,OAAOA,MAGT,SAASI,gBAAgBc,UAAWC,QAClC,IAAIC,OAASD,QAAU,GACvB,IAAK,IAAIE,OAAO1E,mBACdyE,OAAOC,KAAOH,UAAUG;oBAc1B;OAVID,OAAOZ,SAASc,WAAW,OAC7BF,OAAOZ,SAAWY,OAAOZ,SAASe,MAAM,GAAI;;AAG1B,KAAhBH,OAAOI,OACTJ,OAAOI,KAAOC,OAAOL,OAAOI;;AAG9BJ,OAAOM,KAAON,OAAOpH,OAASoH,OAAOO,SAAWP,OAAOpH,OAASoH,OAAOO,SAEhEP,OAGT,SAASQ,sBAAsBC,MAAOC,SACpC,IAAIC,UACJ,IAAK,IAAIC,UAAUF,QACbD,MAAMb,KAAKgB,UACbD,UAAYD,QAAQE,eACbF,QAAQE,SAGnB,OAAsB,OAAdD,WAA2C,oBAAdA,eACnCE,EAAYC,OAAOH,WAAWI,OAGlC,SAAS7E,gBAAgBZ,KAAM0F,QAASC;;AAEtC,SAASC,YAAYC;;AAEflG,WAAWzB,MAAM0B,oBACnB1B,MAAM0B,kBAAkBtI,KAAMA,KAAKH,aAErCe,OAAOC,OAAOb,KAAMuO,YAAc,IAClCvO,KAAK0I,KAAOA,KACZ1I,KAAKoO,QAAUpO,KAAKgL,MAAQoD,QAAU,KAAOpO,KAAKgL,MAAMoD,QAAUA;gDAepE;OAXAE,YAAYzE,UAAY,IAAKwE,WAAazH,OAC1ChG,OAAOiL,iBAAiByC,YAAYzE,UAAW,CAC7ChK,YAAa,CACXkM,MAAOuC,YACP3B,YAAY,GAEdpM,KAAM,CACJwL,MAAO,UAAYrD,KAAO,IAC1BiE,YAAY,KAGT2B,YAGT,SAASE,eAAe1C,QAASnE,OAC/B,IAAK,IAAIoB,SAASH,OAChBkD,QAAQ2C,eAAe1F,MAAOF,cAAcE,QAE9C+C,QAAQnB,GAAG,QAASb,QACpBgC,QAAQlC,QAAQjC,OASlB,SAASwE,SAASJ,OAChB,MAAwB,iBAAVA,OAAsBA,iBAAiBmC,OAGvD,SAAS7F,WAAW0D,OAClB,MAAwB,mBAAVA,MAhjBhBhC,oBAAoBF,UAAYjJ,OAAOkI,OAAOxB,WAASuC,WAEvDE,oBAAoBF,UAAU6E,MAAQ,WACpCF,eAAexO,KAAK2O,iBACpB3O,KAAK2O,gBAAgBD,QACrB1O,KAAKoJ,KAAK,UAGZW,oBAAoBF,UAAUD,QAAU,SAAUjC,OAGhD,OAFA6G,eAAexO,KAAK2O,gBAAiBhH,OACrCiC,QAAQM,KAAKlK,KAAM2H,OACZ3H;;AAIT+J,oBAAoBF,UAAU+E,MAAQ,SAAUC,KAAMC,SAAU7C;;AAE9D,GAAIjM,KAAKsK,QACP,MAAM,IAAIX;mDAIZ;IAAKwC,SAAS0C,QA6hBhB,SAASE,WAAShD,OAChB,MAAwB,iBAAVA,OAAuB,WAAYA,MA9hBzBgD,CAASF,MAC/B,MAAM,IAAItF,UAAU,iDAElBlB,WAAWyG,YACb7C,SAAW6C,SACXA,SAAW;;;AAKO,IAAhBD,KAAKxI;;AAOLrG,KAAKyK,mBAAqBoE,KAAKxI,QAAUrG,KAAKoK,SAASkB,eACzDtL,KAAKyK,oBAAsBoE,KAAKxI,OAChCrG,KAAK0K,oBAAoB3F,KAAK,CAAE8J,KAAMA,KAAMC,SAAUA,WACtD9O,KAAK2O,gBAAgBC,MAAMC,KAAMC,SAAU7C,YAI3CjM,KAAKoJ,KAAK,QAAS,IAAIM,4BACvB1J,KAAK0O,SAdDzC,UACFA;;AAkBNlC,oBAAoBF,UAAUiD,IAAM,SAAU+B,KAAMC,SAAU7C;;AAY5D;;AAVI5D,WAAWwG,OACb5C,SAAW4C,KACXA,KAAOC,SAAW,MAEXzG,WAAWyG,YAClB7C,SAAW6C,SACXA,SAAW,MAIRD,KAIA,CACH,IAAIjE,KAAO5K,KACPgP,eAAiBhP,KAAK2O,gBAC1B3O,KAAK4O,MAAMC,KAAMC,UAAU,WACzBlE,KAAKP,QAAS,EACd2E,eAAelC,IAAI,KAAM,KAAMb,aAEjCjM,KAAKsK,SAAU,OAVftK,KAAKqK,OAASrK,KAAKsK,SAAU,EAC7BtK,KAAK2O,gBAAgB7B,IAAI,KAAM,KAAMb;;AAczClC,oBAAoBF,UAAUoF,UAAY,SAAU1O,KAAMwL,OACxD/L,KAAKoK,SAAS0D,QAAQvN,MAAQwL,MAC9B/L,KAAK2O,gBAAgBM,UAAU1O,KAAMwL;;AAIvChC,oBAAoBF,UAAUqF,aAAe,SAAU3O,aAC9CP,KAAKoK,SAAS0D,QAAQvN,MAC7BP,KAAK2O,gBAAgBO,aAAa3O;;AAIpCwJ,oBAAoBF,UAAUsF,WAAa,SAAUC,MAAOnD,UAC1D,IAAIrB,KAAO5K;iCAGX;SAASqP,iBAAiBC,QACxBA,OAAOH,WAAWC,OAClBE,OAAOb,eAAe,UAAWa,OAAO1F,SACxC0F,OAAOC,YAAY,UAAWD,OAAO1F;6CAIvC;SAAS4F,WAAWF,QACd1E,KAAK6E,UACPC,aAAa9E,KAAK6E,UAEpB7E,KAAK6E,SAAWN,YAAW,WACzBvE,KAAKxB,KAAK,WACVuG,eACCP,OACHC,iBAAiBC;kCAInB;SAASK;;AAEH/E,KAAK6E,WACPC,aAAa9E,KAAK6E,UAClB7E,KAAK6E,SAAW;;AAIlB7E,KAAK6D,eAAe,QAASkB,YAC7B/E,KAAK6D,eAAe,QAASkB,YAC7B/E,KAAK6D,eAAe,WAAYkB,YAChC/E,KAAK6D,eAAe,QAASkB,YACzB1D,UACFrB,KAAK6D,eAAe,UAAWxC,UAE5BrB,KAAK0E,QACR1E,KAAK+D,gBAAgBF,eAAe,SAAUe;4BAwBlD;OAnBIvD,UACFjM,KAAK2K,GAAG,UAAWsB;;AAIjBjM,KAAKsP,OACPE,WAAWxP,KAAKsP,QAGhBtP,KAAK2O,gBAAgBiB,KAAK,SAAUJ;;AAItCxP,KAAK2K,GAAG,SAAU0E,kBAClBrP,KAAK2K,GAAG,QAASgF,YACjB3P,KAAK2K,GAAG,QAASgF,YACjB3P,KAAK2K,GAAG,WAAYgF,YACpB3P,KAAK2K,GAAG,QAASgF,YAEV3P;;AAIT,CACE,eAAgB,YAChB,aAAc,sBACdyE,SAAQ,SAAUoL,QAClB9F,oBAAoBF,UAAUgG,QAAU,SAAUC,EAAGC,GACnD,OAAO/P,KAAK2O,gBAAgBkB,QAAQC,EAAGC;;AAK3C,CAAC,UAAW,aAAc,UAAUtL,SAAQ,SAAUuL,UACpDpP,OAAOqP,eAAelG,oBAAoBF,UAAWmG,SAAU,CAC7DlP,IAAK,WAAc,OAAOd,KAAK2O,gBAAgBqB,gBAInDjG,oBAAoBF,UAAUM,iBAAmB,SAAUH;;AAkBzD;;AAhBKA,QAAQ8D,UACX9D,QAAQ8D,QAAU;;;;AAMhB9D,QAAQuC;;AAELvC,QAAQwC,WACXxC,QAAQwC,SAAWxC,QAAQuC,aAEtBvC,QAAQuC,OAIZvC,QAAQ2D,UAAY3D,QAAQ0D,KAAM,CACrC,IAAIwC,UAAYlG,QAAQ0D,KAAKyC,QAAQ,KACjCD,UAAY,EACdlG,QAAQ2D,SAAW3D,QAAQ0D,MAG3B1D,QAAQ2D,SAAW3D,QAAQ0D,KAAK0C,UAAU,EAAGF,WAC7ClG,QAAQhE,OAASgE,QAAQ0D,KAAK0C,UAAUF;;AAO9CnG,oBAAoBF,UAAUoB,gBAAkB;;AAE9C,IAAIS,SAAW1L,KAAKoK,SAASsB,SACzBC,eAAiB3L,KAAKoK,SAASmB,gBAAgBG,UACnD,IAAKC,eACH,MAAM,IAAIpC,UAAU,wBAA0BmC;;iDAKhD;GAAI1L,KAAKoK,SAASiG,OAAQ,CACxB,IAAI5E,OAASC,SAAS6B,MAAM,GAAI,GAChCvN,KAAKoK,SAASkG,MAAQtQ,KAAKoK,SAASiG,OAAO5E;0DAI7C;IAAIK,QAAU9L,KAAK2O,gBACbhD,eAAeG,QAAQ9L,KAAKoK,SAAUpK,KAAK6K,mBAEjD,IAAK,IAAI9B,SADT+C,QAAQ3C,cAAgBnJ,KACN4I,QAChBkD,QAAQnB,GAAG5B,MAAOF,cAAcE;;;;;AAalC,GARA/I,KAAKuQ,YAAc,MAAMvD,KAAKhN,KAAKoK,SAASsD,MAC1CjN,IAAI+P,OAAOxQ,KAAKoK;;;AAGhBpK,KAAKoK,SAASsD,KAIZ1N,KAAKyQ,YAAa;;AAEpB,IAAIC,EAAI,EACJ9F,KAAO5K,KACP2Q,QAAU3Q,KAAK0K,qBAClB,SAASkG,UAAUjJ;;;AAGlB,GAAImE,UAAYlB,KAAK+D;;;AAGnB,GAAIhH,MACFiD,KAAKxB,KAAK,QAASzB,YAGhB,GAAI+I,EAAIC,QAAQtK,OAAQ,CAC3B,IAAIwK,OAASF,QAAQD;uBAEhB5E;QAAQgF,UACXhF,QAAQ8C,MAAMiC,OAAOhC,KAAMgC,OAAO/B,SAAU8B;;KAIvChG,KAAKP,QACZyB,QAAQgB,MAnBd;;AA2BJ/C,oBAAoBF,UAAUkB,iBAAmB,SAAUD;;AAEzD,IAAIiG,WAAajG,SAASiG,WACtB/Q,KAAKoK,SAAS4G,gBAChBhR,KAAKwK,WAAWzF,KAAK,CACnBtE,IAAKT,KAAKuQ,YACVzC,QAAShD,SAASgD,QAClBiD,WAAYA;;;;;;;qDAYhB;IAwBIE,eAxBAC,SAAWpG,SAASgD,QAAQoD,SAChC,IAAKA,WAA8C,IAAlClR,KAAKoK,SAAS+G,iBAC3BJ,WAAa,KAAOA,YAAc,IAOpC,OANAjG,SAASsG,YAAcpR,KAAKuQ,YAC5BzF,SAASuG,UAAYrR,KAAKwK,WAC1BxK,KAAKoJ,KAAK,WAAY0B;;AAGtB9K,KAAK0K,oBAAsB;;;;AAW7B,GANA8D,eAAexO,KAAK2O;;AAEpB7D,SAASlB,YAIH5J,KAAKuK,eAAiBvK,KAAKoK,SAASiB,aACxC,MAAM,IAAI5B;0CAKZ;IAAI6H,eAAiBtR,KAAKoK,SAASkH,eAC/BA,iBACFL,eAAiBrQ,OAAOC,OAAO;;AAE7B0Q,KAAMzG,SAAS0G,IAAIC,UAAU,SAC5BzR,KAAKoK,SAAS0D;;;;kEAOnB;IAAI+B,OAAS7P,KAAKoK,SAASyF,SACP,MAAfkB,YAAqC,MAAfA,aAAgD,SAAzB/Q,KAAKoK,SAASyF;;;;;AAK5C,MAAfkB,aAAwB,iBAAiB/D,KAAKhN,KAAKoK,SAASyF,WAC/D7P,KAAKoK,SAASyF,OAAS;;AAEvB7P,KAAK0K,oBAAsB,GAC3BkD,sBAAsB,aAAc5N,KAAKoK,SAAS0D;uEAIpD;IAAI4D,kBAAoB9D,sBAAsB,UAAW5N,KAAKoK,SAAS0D,SAGnE6D,gBAAkBtF,SAASrM,KAAKuQ,aAChCqB,YAAcF,mBAAqBC,gBAAgBpF,KACnDsF,WAAa,QAAQ7E,KAAKkE,UAAYlR,KAAKuQ,YAC7C9P,IAAI+P,OAAO5P,OAAOC,OAAO8Q,gBAAiB,CAAEpF,KAAMqF,eAGhDE,YAoHN,SAASC,WAAWC,SAAUC;;AAE5B,OAAOxJ,aAAe,IAAIxB,MAAI+K,SAAUC,MAAQ5F,SAAS5L,IAAIyR,QAAQD,KAAMD,WAtHzDD,CAAWb,SAAUW;;;AAevC,GAdA9K,MAAM,iBAAkB+K,YAAY7E,MACpCjN,KAAKyQ,aAAc,EACnBrE,gBAAgB0F,YAAa9R,KAAKoK;;;CAI9B0H,YAAYpG,WAAaiG,gBAAgBjG,UACjB,WAAzBoG,YAAYpG,UACZoG,YAAYvF,OAASqF,cA6L1B,SAASO,YAAYC,UAAWC,QAC9B7K,OAAO2E,SAASiG,YAAcjG,SAASkG,SACvC,IAAIC,IAAMF,UAAU/L,OAASgM,OAAOhM,OAAS,EAC7C,OAAOiM,IAAM,GAAwB,MAAnBF,UAAUE,MAAgBF,UAAUG,SAASF,QA/L3DF,CAAYL,YAAYvF,KAAMqF,eAChChE,sBAAsB,yCAA0C5N,KAAKoK,SAAS0D,SAI5EzF,WAAWiJ,gBAAiB,CAC9B,IAAIkB,gBAAkB,CACpB1E,QAAShD,SAASgD,QAClBiD,WAAYA,YAEV0B,eAAiB,CACnBhS,IAAKoR,WACLhC,OAAQA,OACR/B,QAASmD,gBAEXK,eAAetR,KAAKoK,SAAUoI,gBAAiBC,gBAC/CzS,KAAKmK,iBAAiBnK,KAAKoK;iCAI7BpK;KAAKiL,mBA+LPyH,wBAAiBxH,KAAK,CAAEhE,KAAMA,KAAME,MAAOA,qCACrB8D,iDC7qBtB,MAAMyH,UCAW,MAAMA,UACrB9S,YAAa+S,KACX,KAAMA,IAAM,IAA4B,IAApBA,IAAM,EAAKA,KAAY,MAAM,IAAIhM,MAAM,qDAC3D5G,KAAK6Q,OAAS,IAAI3N,MAAM0P,KACxB5S,KAAK6S,KAAOD,IAAM,EAClB5S,KAAK8S,IAAM,EACX9S,KAAK+S,IAAM,EACX/S,KAAKgT,KAAO,KAGdC,QACEjT,KAAK8S,IAAM9S,KAAK+S,IAAM,EACtB/S,KAAKgT,KAAO,KACZhT,KAAK6Q,OAAOqC,UAAKjF,GAGnBlJ,KAAM8J,MACJ,YAA8BZ,IAA1BjO,KAAK6Q,OAAO7Q,KAAK8S,OACrB9S,KAAK6Q,OAAO7Q,KAAK8S,KAAOjE,KACxB7O,KAAK8S,IAAO9S,KAAK8S,IAAM,EAAK9S,KAAK6S,MAC1B,GAGTM,QACE,MAAMC,KAAOpT,KAAK6Q,OAAO7Q,KAAK+S,KAC9B,QAAa9E,IAATmF,KAGJ,OAFApT,KAAK6Q,OAAO7Q,KAAK+S,UAAO9E,EACxBjO,KAAK+S,IAAO/S,KAAK+S,IAAM,EAAK/S,KAAK6S,KAC1BO,KAGTC,OACE,OAAOrT,KAAK6Q,OAAO7Q,KAAK+S,KAG1BO,UACE,YAAiCrF,IAA1BjO,KAAK6Q,OAAO7Q,KAAK+S,WDlC5BQ,SAAiB,MAAMC,SACrB3T,YAAa+S,KACX5S,KAAK4S,IAAMA,KAAO,GAClB5S,KAAKyT,KAAO,IAAId,UAAU3S,KAAK4S,KAC/B5S,KAAK0T,KAAO1T,KAAKyT,KACjBzT,KAAKqG,OAAS,EAGhB4M,QACEjT,KAAKyT,KAAOzT,KAAK0T,KACjB1T,KAAKyT,KAAKR,QACVjT,KAAKqG,OAAS,EAGhBtB,KAAM4O,KAEJ,GADA3T,KAAKqG,UACArG,KAAKyT,KAAK1O,KAAK4O,KAAM,CACxB,MAAMC,KAAO5T,KAAKyT,KAClBzT,KAAKyT,KAAOG,KAAKZ,KAAO,IAAIL,UAAU,EAAI3S,KAAKyT,KAAK5C,OAAOxK,QAC3DrG,KAAKyT,KAAK1O,KAAK4O,MAInBR,QACsB,IAAhBnT,KAAKqG,QAAcrG,KAAKqG,SAC5B,MAAMsN,IAAM3T,KAAK0T,KAAKP,QACtB,QAAYlF,IAAR0F,KAAqB3T,KAAK0T,KAAKV,KAAM,CACvC,MAAMA,KAAOhT,KAAK0T,KAAKV,KAGvB,OAFAhT,KAAK0T,KAAKV,KAAO,KACjBhT,KAAK0T,KAAOV,KACLhT,KAAK0T,KAAKP,QAGnB,OAAOQ,IAGTN,OACE,MAAMM,IAAM3T,KAAK0T,KAAKL,OACtB,YAAYpF,IAAR0F,KAAqB3T,KAAK0T,KAAKV,KAAahT,KAAK0T,KAAKV,KAAKK,OACxDM,IAGTL,UACE,OAAuB,IAAhBtT,KAAKqG,SE2BhB,SAASwN,SAAUhD,QACjB,OAAIiD,OAAO/E,SAAS8B,QAAgBA,OAC7BiD,OAAOzQ,KAAKwN,OAAOA,OAAQA,OAAOkD,WAAYlD,OAAOmD,gBA2E9DC,MAAiB,CACflF,SAtJF,SAASA,SAAUhD,OACjB,OAAO+H,OAAO/E,SAAShD,QAAUA,iBAAiBmI,YAsJlDC,WAnJF,SAASA,WAAYrF,UACnB,OAAOgF,OAAOK,WAAWrF,WAmJzBsF,MAhJF,SAASA,MAAOC,KAAMnB,KAAMpE,UAC1B,OAAOgF,OAAOM,MAAMC,KAAMnB,KAAMpE,WAgJhCwF,YA7IF,SAASA,YAAaD,MACpB,OAAOP,OAAOQ,YAAYD,OA6I1BE,gBA1IF,SAASA,gBAAiBF,MACxB,OAAOP,OAAOS,gBAAgBF,OA0I9BL,WAvIF,SAASA,WAAYQ,OAAQ1F,UAC3B,OAAOgF,OAAOE,WAAWQ,OAAQ1F,WAuIjC/N,QApIF,SAASA,QAAS+O,EAAGC,GACnB,OAAO+D,OAAO/S,QAAQ+O,EAAGC,IAoIzB0E,OAjIF,SAASA,OAAQ9D,QAAS+D,aACxB,OAAOZ,OAAOW,OAAO9D,QAAS+D,cAiI9BC,KA9HF,SAASA,KAAMC,OAAQzH,OAAQ0H,YAAaC,MAAOhI,KACjD,OAAO+G,SAASe,QAAQD,KAAKxH,OAAQ0H,YAAaC,MAAOhI,MA8HzDiI,OA3HF,SAASA,OAAQjF,EAAGC,GAClB,OAAO8D,SAAS/D,GAAGiF,OAAOhF,IA2H1BmD,KAxHF,SAASA,KAAMrC,OAAQ9E,MAAOiJ,OAAQlI,IAAKgC,UACzC,OAAO+E,SAAShD,QAAQqC,KAAKnH,MAAOiJ,OAAQlI,IAAKgC,WAwHjDzL,KArHF,SAASA,KAAM0I,MAAOkJ,iBAAkB5O,QACtC,OAAOyN,OAAOzQ,KAAK0I,MAAOkJ,iBAAkB5O,SAqH5C6O,SAlHF,SAASA,SAAUrE,OAAQ9E,MAAOgI,WAAYjF,UAC5C,OAAO+E,SAAShD,QAAQqE,SAASnJ,MAAOgI,WAAYjF,mBAGtD,SAASqB,UAASU,OAAQ9E,MAAOoJ,WAAYrG,UAC3C,OAAO+E,SAAShD,QAAQV,QAAQpE,MAAOoJ,WAAYrG,WA+GnDsG,YA5GF,SAASA,YAAavE,OAAQ9E,MAAOgI,WAAYjF,UAC/C,OAAO+E,SAAShD,QAAQuE,YAAYrJ,MAAOgI,WAAYjF,WA4GvDuG,OAzGF,SAASA,OAAQxE,QACf,OAAOgD,SAAShD,QAAQwE,UAyGxBC,OAtGF,SAASA,OAAQzE,QACf,OAAOgD,SAAShD,QAAQyE,UAsGxBC,OAnGF,SAASA,OAAQ1E,QACf,OAAOgD,SAAShD,QAAQ0E,UAmGxB1B,SAAAA,SACA1T,SA5FF,SAASA,SAAU0Q,OAAQ/B,SAAUgG,MAAOhI,KAC1C,OAAO+G,SAAShD,QAAQ1Q,SAAS2O,SAAUgG,MAAOhI,MA4FlD8B,MAzFF,SAASA,MAAOiC,OAAQ2D,OAAQQ,OAAQ3O,OAAQyI,UAC9C,OAAO+E,SAAShD,QAAQjC,MAAM4F,OAAQQ,OAAQ3O,OAAQyI,WAyFtD0G,cAtFF,SAASA,cAAe3E,OAAQ9E,MAAOiJ,QACrC,OAAOnB,SAAShD,QAAQ2E,cAAczJ,MAAOiJ,SAsF7CS,aAnFF,SAASA,aAAc5E,OAAQ9E,MAAOiJ,QACpC,OAAOnB,SAAShD,QAAQ4E,aAAa1J,MAAOiJ,SAmF5CU,cAhFF,SAASA,cAAe7E,OAAQ9E,MAAOiJ,QACrC,OAAOnB,SAAShD,QAAQ6E,cAAc3J,MAAOiJ,SAgF7CW,aA7EF,SAASA,aAAc9E,OAAQ9E,MAAOiJ,QACpC,OAAOnB,SAAShD,QAAQ8E,aAAa5J,MAAOiJ,SA6E5CY,aA1EF,SAASA,aAAc/E,OAAQmE,QAC7B,OAAOnB,SAAShD,QAAQ+E,aAAaZ,SA0ErCa,YAvEF,SAASA,YAAahF,OAAQmE,QAC5B,OAAOnB,SAAShD,QAAQgF,YAAYb,SAuEpCc,aApEF,SAASA,aAAcjF,OAAQmE,QAC7B,OAAOnB,SAAShD,QAAQiF,aAAad,SAoErCe,YAjEF,SAASA,YAAalF,OAAQmE,QAC5B,OAAOnB,SAAShD,QAAQkF,YAAYf,SAiEpCgB,cA9DF,SAASA,cAAenF,OAAQ9E,MAAOiJ,QACrC,OAAOnB,SAAShD,QAAQmF,cAAcjK,MAAOiJ,SA8D7CiB,aA3DF,SAASA,aAAcpF,OAAQ9E,MAAOiJ,QACpC,OAAOnB,SAAShD,QAAQoF,aAAalK,MAAOiJ,SA2D5CkB,cAxDF,SAASA,cAAerF,OAAQ9E,MAAOiJ,QACrC,OAAOnB,SAAShD,QAAQqF,cAAcnK,MAAOiJ,SAwD7CmB,aArDF,SAASA,aAActF,OAAQ9E,MAAOiJ,QACpC,OAAOnB,SAAShD,QAAQsF,aAAapK,MAAOiJ,SAqD5CoB,aAlDF,SAASA,aAAcvF,OAAQmE,QAC7B,OAAOnB,SAAShD,QAAQuF,aAAapB,SAkDrCqB,YA/CF,SAASA,YAAaxF,OAAQmE,QAC5B,OAAOnB,SAAShD,QAAQwF,YAAYrB,SA+CpCsB,aA5CF,SAASA,aAAczF,OAAQmE,QAC7B,OAAOnB,SAAShD,QAAQyF,aAAatB,SA4CrCuB,YAzCF,SAASA,YAAa1F,OAAQmE,QAC5B,OAAOnB,SAAShD,QAAQ0F,YAAYvB,UClJtC,MAAMf,MAAMjN,MCAZ,MAAMiN,MAAMjN;;;GCAZ,MAAMwP,mBFEW,MAAMA,mBACrB3W,YAAaiP,UACX9O,KAAK8O,SAAWA,SAGd2H,gBACF,OAAO,EAGTC,OAAQhD,MACN,OAAOO,MAAI9T,SAASuT,KAAM1T,KAAK8O,UAGjC6H,QACE,MAAO,KEfLC,YDIW,MAAMA,YACrB/W,cACEG,KAAK6W,UAAY,EACjB7W,KAAK8W,UAAY,EACjB9W,KAAK+W,YAAc,EACnB/W,KAAKgX,cAAgB,IACrBhX,KAAKiX,cAAgB,IAGnBR,gBACF,OAAOzW,KAAK8W,UAGdJ,OAAQ7H;;AAEN,GAAyB,IAArB7O,KAAK+W,YAAmB,CAC1B,IAAIG,YAAa,EAEjB,IAAK,IAAIxG,EAAIyG,KAAKC,IAAI,EAAGvI,KAAKmF,WAAa,GAAIqD,EAAIxI,KAAKmF,WAAYtD,EAAI2G,GAAKH,WAAYxG,IACvFwG,WAAarI,KAAK6B,IAAM,IAG1B,GAAIwG,WAAY,OAAOjD,MAAI9T,SAAS0O,KAAM,QAG5C,IAAIzI,OAAS,GAEb,IAAK,IAAIsK,EAAI,EAAG2G,EAAIxI,KAAKmF,WAAYtD,EAAI2G,EAAG3G,IAAK,CAC/C,MAAM4G,KAAOzI,KAAK6B,GAEO,IAArB1Q,KAAK+W,YA2BLO,KAAOtX,KAAKgX,eAAiBM,KAAOtX,KAAKiX,eAC3CjX,KAAK6W,UAAY,EACjB7W,KAAK+W,YAAc,EACnB/W,KAAK8W,UAAY,EACjB9W,KAAKgX,cAAgB,IACrBhX,KAAKiX,cAAgB,IAErB7Q,QAAU,MAKZpG,KAAKgX,cAAgB,IACrBhX,KAAKiX,cAAgB,IAErBjX,KAAK6W,UAAa7W,KAAK6W,WAAa,EAAa,GAAPS,KAC1CtX,KAAK8W,YAED9W,KAAK8W,YAAc9W,KAAK+W,cAE5B3Q,QAAU8H,OAAOqJ,cAAcvX,KAAK6W,WAEpC7W,KAAK6W,UAAY,EACjB7W,KAAK+W,YAAc,EACnB/W,KAAK8W,UAAY,IAlDXQ,MAAQ,IACVlR,QAAU8H,OAAOsJ,aAAaF,OAE9BtX,KAAK8W,UAAY,EAEbQ,MAAQ,KAAQA,MAAQ,KAC1BtX,KAAK+W,YAAc,EACnB/W,KAAK6W,UAAmB,GAAPS,MACRA,MAAQ,KAAQA,MAAQ,KACpB,MAATA,KAAetX,KAAKgX,cAAgB,IACtB,MAATM,OAAetX,KAAKiX,cAAgB,KAC7CjX,KAAK+W,YAAc,EACnB/W,KAAK6W,UAAmB,GAAPS,MACRA,MAAQ,KAAQA,MAAQ,KACpB,MAATA,OAAetX,KAAKgX,cAAgB,KAC3B,MAATM,OAAetX,KAAKiX,cAAgB,KACxCjX,KAAK+W,YAAc,EACnB/W,KAAK6W,UAAmB,EAAPS,MAEjBlR,QAAU,KAkClB,OAAOA,OAGTuQ,QACE,MAAMvQ,OAASpG,KAAK+W,YAAc,EAAI,IAAW,GAQjD,OANA/W,KAAK6W,UAAY,EACjB7W,KAAK+W,YAAc,EACnB/W,KAAK8W,UAAY,EACjB9W,KAAKgX,cAAgB,IACrBhX,KAAKiX,cAAgB,IAEd7Q,SErGX,MAAMqR,aAAEA,cAAiBzQ,8BACnB0Q,iBAAmB,IAAI9Q,MAAM,wBAC7B+Q,gBAAkB,IAAI/Q,MAAM,mBAE5BgR,OAAOzQ,SACP0Q,YDFW,MAAMA,YACrBhY,YAAaiP,SAAW,QAGtB,OAFA9O,KAAK8O,SAoCT,SAASgJ,kBAAmBhJ,UAG1B,OAFAA,SAAWA,SAAShN,eAGlB,IAAK,OACL,IAAK,QACH,MAAO,OACT,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,MAAO,UACT,IAAK,SACL,IAAK,SACH,MAAO,SACT,IAAK,SACL,IAAK,QACL,IAAK,MACH,OAAOgN,SACT,QACE,MAAM,IAAIlI,MAAM,qBAAuBkI,WAxDzBgJ,CAAkBhJ,UAE1B9O,KAAK8O,UACX,IAAK,OACH9O,KAAK+X,QAAU,IAAInB,YACnB,MACF,IAAK,UACL,IAAK,SACH,MAAM,IAAIhQ,MAAM,yBAA2B5G,KAAK8O,UAClD,QACE9O,KAAK+X,QAAU,IAAIvB,mBAAmBxW,KAAK8O,WAI7C2H,gBACF,OAAOzW,KAAK+X,QAAQtB,UAGtB1R,KAAM8J,MACJ,MAAoB,iBAATA,KAA0BA,KAC9B7O,KAAK+X,QAAQrB,OAAO7H;;AAI7BD,MAAOC,MACL,OAAO7O,KAAK+E,KAAK8J,MAGnB/B,IAAK+B,MACH,IAAIzI,OAAS,GAGb,OAFIyI,OAAMzI,OAASpG,KAAK+E,KAAK8J,OAC7BzI,QAAUpG,KAAK+X,QAAQpB,QAChBvQ,SC7BL4R,IAAgC,oBAAnBC,eAAiCC,IAAMC,eAAOnQ,QAAQoQ,SAASF,IAAMD,eA6GlFI,cAAgBC,OAAOD,eAAiBC,OAAO,iBAErD,MAAMC,cACJ1Y,YAAa2Y,QAAQC,cAAEA,cAAgB,MAAKxX,IAAEA,IAAM,KAAIyX,YAAEA,YAAW1E,WAAEA,WAAU2E,mBAAEA,oBAAuB,IACxG3Y,KAAKwY,OAASA,OACdxY,KAAK4Y,MAAQ,IAAIhB,OACjB5X,KAAKyY,cAAgBA,cACrBzY,KAAK6Y,SAAW,EAChB7Y,KAAK2H,MAAQ,KACb3H,KAAK8Y,SAAW,KAChB9Y,KAAK+Y,OAAS;AACd/Y,KAAKgU,WAAa2E,oBAAsB3E,YAAcgF,kBACtDhZ,KAAKiB,IAAMyX,aAAezX,IAC1BjB,KAAKiZ,WAAaA,WAAWtU,KAAK3E,MAClCA,KAAKkZ,oBAAsBC,cAAcxU,KAAK3E,MAG5CoZ,YACF,OAAmD,IArE9B,QAqEbpZ,KAAKwY,OAAOa,cAGtBtU,KAAM8J,MACJ,OAAqD,IAxBjCyK,UAwBftZ,KAAKwY,OAAOa,gBACA,OAAbrZ,KAAKiB,MAAc4N,KAAO7O,KAAKiB,IAAI4N,OAEvC7O,KAAK6Y,UAAY7Y,KAAKgU,WAAWnF,MACjC7O,KAAK4Y,MAAM7T,KAAK8J,MAEZ7O,KAAK6Y,SAAW7Y,KAAKyY,eACvBzY,KAAKwY,OAAOa,cAlFO,SAmFZ,IAGTrZ,KAAKwY,OAAOa,cA5CmBE,SA6CxB,IAGTpG,QACE,MAAMtE,KAAO7O,KAAK4Y,MAAMzF,QAKxB,OAHAnT,KAAK6Y,UAAY7Y,KAAKgU,WAAWnF,MACX,IAAlB7O,KAAK6Y,WAAgB7Y,KAAKwY,OAAOa,cAjFbG,WAmFjB3K,KAGT/B,IAAK+B,MACiB,mBAATA,KAAqB7O,KAAKwY,OAAO5I,KAAK,SAAUf,MAClDA,MAAAA,MAAqC7O,KAAK+E,KAAK8J,MACxD7O,KAAKwY,OAAOa,aA5FYG,WAJH,UAgGOxZ,KAAKwY,OAAOa,cAG1CI,UAAW5K,KAAM6K,IACf,MAAM7I,OAAS,GACT2H,OAASxY,KAAKwY,OAGpB,IADA3H,OAAO9L,KAAK8J,MAlEgB0K,UAEXI,UAiETnB,OAAOa,eACbxI,OAAO9L,KAAKyT,OAAOoB,eAAezG,SAGpC,GAA4C,IA3F5B0G,GA2FXrB,OAAOa,cAAmC,OAAOK,GAAG,MACzDlB,OAAOsB,QAAQjJ,OAAQ6I,IAGzBK,SACE,MAAMvB,OAASxY,KAAKwY,OAEpBA,OAAOa,cA3Hc,OA6HrB,EAAG,CACD,KA5HmB,UA6CJM,UA+EPnB,OAAOa,eAA+C,CAC5D,MAAMxK,KAAO7O,KAAKmT,QAClBqF,OAAOa,cA/EkBW,SAgFzBxB,OAAOyB,OAAOpL,KAAM7O,KAAKiZ,YAG8B,IApF9BiB,QAoFtB1B,OAAOa,eAAgDrZ,KAAKma,0BAChC,IAA1Bna,KAAKoa,kBAEd5B,OAAOa,cAtHiBG,UAyH1BW,mBACE,MAAM3B,OAASxY,KAAKwY,OAEpB,GArIqB,YA0CMmB,UA2FtBnB,OAAOa,cAGV,OAFAb,OAAOa,aA/IY,OA+IGb,OAAOa,kBAC7Bb,OAAO6B,OAAOC,WAAW3V,KAAK3E,OA3Ld,IAoECua,GA2Hd/B,OAAOa,cAjMM,IA4EHM,SA6HVnB,OAAOa,gBACVb,OAAOa,aArMOG,WA8DLgB,OAuIchC,OAAOa,cAC9Bb,OAAOiC,MAAMC,UAAU/V,KAAK3E,QATsB,IAxH9B2a,SAwHfnC,OAAOa,gBACVb,OAAOa,cAhIAmB,OAiIPhC,OAAOoC,SAASC,aAAalW,KAAK3E,QAWxCoa,iBACE,OAAqD,IA5JhC,SA4JhBpa,KAAKwY,OAAOa,gBACjBrZ,KAAKwY,OAAOa,cAnJYG,WAoJjB,GAGTsB,iBAtKuB,UAmDQC,SAoHxB/a,KAAKwY,OAAOa,cAA4DrZ,KAAK+Z,SAC7E/Z,KAAKgb,iBAGZA,iBACuD,IAvKhC,SAuKhBhb,KAAKwY,OAAOa,gBACjBrZ,KAAKwY,OAAOa,cAxKS,SAyK+B,IA/K/B,OA+KhBrZ,KAAKwY,OAAOa,eAAsCrB,IAAIhY,KAAKkZ,uBAIpE,MAAM+B,cACJpb,YAAa2Y,QAAQC,cAAEA,cAAgB,MAAKxX,IAAEA,IAAM,KAAIia,YAAEA,YAAWlH,WAAEA,WAAUmH,mBAAEA,oBAAuB,IACxGnb,KAAKwY,OAASA,OACdxY,KAAK4Y,MAAQ,IAAIhB,OACjB5X,KAAKyY,cAAkC,IAAlBA,cAAsB,EAAIA,cAC/CzY,KAAK6Y,SAAW,EAChB7Y,KAAKob,UAAY3C,cAAgB,EACjCzY,KAAK2H,MAAQ,KACb3H,KAAK8Y,SAAW,KAChB9Y,KAAKgU,WAAamH,oBAAsBnH,YAAcgF,kBACtDhZ,KAAKiB,IAAMia,aAAeja,IAC1BjB,KAAKqb,OAAS,KACdrb,KAAKsb,UAAYA,UAAU3W,KAAK3E,MAChCA,KAAKkZ,oBAAsBqC,aAAa5W,KAAK3E,MAG3CoZ,YACF,OAAkD,IA/NxB,MA+NlBpZ,KAAKwY,OAAOa,cAGtBmC,KAAMH,OAAQ3B,IACZ,GAAoB,OAAhB1Z,KAAKqb,OAAiB,MAAM,IAAIzU,MAAM;AAS1C,GARkB,mBAAP8S,KAAmBA,GAAK,MAEnC1Z,KAAKwY,OAAOa,cA3Oc,IA4O1BrZ,KAAKqb,OAASA,OACdrb,KAAK8Y,SAAW,IAAI2C,SAASzb,KAAKwY,OAAQ6C,OAAQ3B,IAE9CA,IAAI1Z,KAAKwY,OAAO7N,GAAG,QAASb,QAE5B4R,UAAUL,QACZA,OAAOzB,eAAed,SAAW9Y,KAAK8Y,SAClCY,IAAI2B,OAAO1Q,GAAG,QAASb;AAC3BuR,OAAO1Q,GAAG,SAAU3K,KAAK8Y,SAAShI,SAASnM,KAAK3E,KAAK8Y,eAChD,CACL,MAAM6C,QAAU3b,KAAK8Y,SAAS8C,KAAKjX,KAAK3E,KAAK8Y,SAAUuC,QACjDQ,QAAU7b,KAAK8Y,SAAS8C,KAAKjX,KAAK3E,KAAK8Y,SAAUuC,OAAQ;AAC/DA,OAAO1Q,GAAG,QAASgR,SACnBN,OAAO1Q,GAAG,QAASkR,SACnBR,OAAO1Q,GAAG,SAAU3K,KAAK8Y,SAAShI,SAASnM,KAAK3E,KAAK8Y,WAGvDuC,OAAO1Q,GAAG,QAASmR,WAAWnX,KAAK3E,OACnCA,KAAKwY,OAAOpP,KAAK,SAAUiS,QAC3BA,OAAOjS,KAAK,OAAQpJ,KAAKwY,QAG3BzT,KAAM8J,MACJ,MAAM2J,OAASxY,KAAKwY,OAEpB,OAAa,OAAT3J,MACF7O,KAAKyY,cAAgB,EACrBD,OAAOa,aApPuBG,WAlBN,KAsQDhB,OAAOa,eACvB,GAGQ,OAAbrZ,KAAKiB,KAEM,QADb4N,KAAO7O,KAAKiB,IAAI4N,QAEd2J,OAAOa,cA1PqBG,UA2PrBxZ,KAAK6Y,SAAW7Y,KAAKyY,gBAIhCzY,KAAK6Y,UAAY7Y,KAAKgU,WAAWnF,MACjC7O,KAAK4Y,MAAM7T,KAAK8J,MAEhB2J,OAAOa,aAlQyBG,WAtBN,IAwRHhB,OAAOa,cAEvBrZ,KAAK6Y,SAAW7Y,KAAKyY,eAG9BtF,QACE,MAAMtE,KAAO7O,KAAK4Y,MAAMzF,QAIxB,OAFAnT,KAAK6Y,UAAY7Y,KAAKgU,WAAWnF,MACX,IAAlB7O,KAAK6Y,WAAgB7Y,KAAKwY,OAAOa,cAzQLG,WA0QzB3K,KAGTkN,QAASlN,MACP,MAAMmN,QAAU,CAAc,OAAbhc,KAAKiB,IAAejB,KAAKiB,IAAI4N,MAAQA,MACtD,KAAO7O,KAAK6Y,SAAW,GAAGmD,QAAQjX,KAAK/E,KAAKmT,SAE5C,IAAK,IAAIzC,EAAI,EAAGA,EAAIsL,QAAQ3V,OAAS,EAAGqK,IAAK,CAC3C,MAAM7B,KAAOmN,QAAQtL,GACrB1Q,KAAK6Y,UAAY7Y,KAAKgU,WAAWnF,MACjC7O,KAAK4Y,MAAM7T,KAAK8J,MAGlB7O,KAAK+E,KAAKiX,QAAQA,QAAQ3V,OAAS,IAGrC4V,OACE,MAAMzD,OAASxY,KAAKwY,OAEpB,GArT0B,MAoEVmB,MAiPXnB,OAAOa,cAA6C,CACvD,MAAMxK,KAAO7O,KAAKmT,QAGlB,OAFoB,OAAhBnT,KAAKqb,SAA+C,IAA5Brb,KAAKqb,OAAOzM,MAAMC,QAAiB2J,OAAOa,cA7RxCG,WA8RiB,IApTvB,KAoTnBhB,OAAOa,eAAsCb,OAAOpP,KAAK,OAAQyF,MAC/DA,KAQT,OALuB,IAAnB7O,KAAKob,YACP5C,OAAOa,cAnTiB,OAoTxBrZ,KAAKgb,kBAGA,KAGTkB,QACE,MAAM1D,OAASxY,KAAKwY,OAEpB,KAvU0B,MAoEVmB,MAmQRnB,OAAOa,eAAwF,IA1TtF8C,IA0T8C3D,OAAOa,eAAoC,CACxG,MAAMxK,KAAO7O,KAAKmT,QACE,OAAhBnT,KAAKqb,SAA+C,IAA5Brb,KAAKqb,OAAOzM,MAAMC,QAAiB2J,OAAOa,cA/SxCG,WAgTiB,IAtUvB,KAsUnBhB,OAAOa,eAAsCb,OAAOpP,KAAK,OAAQyF,OAI1EkL,SACE,MAAMvB,OAASxY,KAAKwY,OAEpBA,OAAOa,cAnVmB,GAqV1B,EAAG,CAGD,IAFArZ,KAAKkc,QAEElc,KAAK6Y,SAAW7Y,KAAKyY,eA5UJ,SA6DNkB,OA+Q4BnB,OAAOa,eACnDb,OAAOa,cAzUoBmB,MA0U3BhC,OAAO4D,MAAMpc,KAAKsb,WAClBtb,KAAKkc,QAzUyBG,OAsDT1C,MAsRlBnB,OAAOa,gBACVb,OAAOa,cAvVe,KAwVtBb,OAAOpP,KAAK,aAG0C,IAlV9BkT,GAkVrB9D,OAAOa,eAA+CrZ,KAAKma,0BAC/B,IAA1Bna,KAAKoa,kBAEd5B,OAAOa,cAxUyBG,UA2UlCW,mBACE,MAAM3B,OAASxY,KAAKwY,OArWM,OAkEHmB,KAqSlBnB,OAAOa,gBACVb,OAAOa,aAlVuBG,WAlBN,MAoWDhB,OAAOa,cAC9Bb,OAAOpP,KAAK,OAnTLmT,UAGQ1C,QAiTVrB,OAAOa,gBAAuCb,OAAOa,cAvX1C,GAwXI,OAAhBrZ,KAAKqb,QAAiBrb,KAAKqb,OAAOvO,OAxXtB,IAoECyN,GAuTd/B,OAAOa,cA7XM,IA4EHM,SAyTVnB,OAAOa,gBACVb,OAAOa,aAjYOG,WA8DLgB,OAmUchC,OAAOa,cAC9Bb,OAAOiC,MAAMC,UAAU/V,KAAK3E,QATsB,IApT9B2a,SAoTfnC,OAAOa,gBACVb,OAAOa,cA5TAmB,OA6TPhC,OAAOoC,SAASC,aAAalW,KAAK3E,QAWxCoa,iBACE,OAAoD,IAxX1B,MAwXrBpa,KAAKwY,OAAOa,gBACjBrZ,KAAKwY,OAAOa,cAtWoBG,WAuWzB,GAGTsB,iBAtY4B,KA0EE0B,MA6TvBxc,KAAKwY,OAAOa,cAA0DrZ,KAAK+Z,SAC3E/Z,KAAKgb,iBAGZyB,uBACiE,IAjUjCC,MAiUzB1c,KAAKwY,OAAOa,gBACjBrZ,KAAKwY,OAAOa,cApYc,MAqYyB,IA/YzB,GA+YrBrZ,KAAKwY,OAAOa,eAAqCrB,IAAIhY,KAAKkZ,sBAGjE8B,iBACsD,IAzY1B,MAyYrBhb,KAAKwY,OAAOa,gBACjBrZ,KAAKwY,OAAOa,cA1Yc,MA2YyB,IArZzB,GAqZrBrZ,KAAKwY,OAAOa,eAAqCrB,IAAIhY,KAAKkZ,uBAInE,MAAMyD,eACJ9c,YAAa2Y,QACXxY,KAAK6O,KAAO,KACZ7O,KAAK4c,eAAiBA,eAAejY,KAAK6T,QAC1CxY,KAAKsa,WAAa,MAItB,MAAMmB,SACJ5b,YAAagd,IAAKC,IAAKpD,IACrB1Z,KAAKqD,KAAOwZ,IACZ7c,KAAK+c,GAAKD,IACV9c,KAAKgd,UAAYtD,GACjB1Z,KAAK2H,MAAQ,KACb3H,KAAKid,gBAAiB,EAGxBnM,WACE9Q,KAAKid,gBAAiB,EAGxBrB,KAAMpD,OAAQ0E,KACRA,MAAKld,KAAK2H,MAAQuV,KAElB1E,SAAWxY,KAAK+c,KAClB/c,KAAK+c,GAAK,KAEQ,OAAd/c,KAAKqD,MAQPmV,SAAWxY,KAAKqD,OAClBrD,KAAKqD,KAAO,KAEI,OAAZrD,KAAK+c,KAQY,OAAnB/c,KAAKgd,WAAoBhd,KAAKgd,UAAUhd,KAAK2H,OACjD3H,KAAK+c,GAAK/c,KAAKqD,KAAOrD,KAAKgd,UAAY,MARO,IAvbpB,MAubjBxE,OAAOa,eACVrZ,KAAK+c,GAAGnT,QAAQ5J,KAAK2H,OAAS,IAAIf,MAAM,yCAZG,IA5avB,MA4ajB5G,KAAKqD,KAAKgW,eAAoCrZ,KAAKid,gBACtDjd,KAAKqD,KAAKuG,QAAQ5J,KAAK2H,OAAS,IAAIf,MAAM,wCAsBpD,SAASkV,aACP9b,KAAKwY,OAAOa,cAzcgB,IA0c5BrZ,KAAK8a,iBAGP,SAASR,WAAY4C,KACnB,MAAM1E,OAASxY,KAAKwY,OAChB0E,KAAK1E,OAAO5O,QAAQsT,KACuB,IAxZ1B3C,GAwZhB/B,OAAOa,gBACVb,OAAOa,cA7ac,QA8arBb,OAAOpP,KAAK,WA3ZHmT,UAGQ1C,QA0ZdrB,OAAOa,gBACVb,OAAOa,cAjeW,GAoepBb,OAAOa,cA3amBG;;AA8aqB,IA3bxB,OA2blBhB,OAAOa,cAAsCrZ,KAAK+Z,SAClD/Z,KAAKgb,iBAGZ,SAASH,aAAcqC,KACrB,MAAM1E,OAASxY,KAAKwY,OAEf0E,KAAOld,KAAK2H,QAAU+P,mBAAkBwF,IAAMld,KAAK2H,OACpDuV,KAAK1E,OAAOpP,KAAK,QAAS8T,KAC9B1E,OAAOa,cA/ea,EAgfpBb,OAAOpP,KAAK,SAEZ,MAAM+T,GAAK3E,OAAO4E,eACZC,GAAK7E,OAAOoB,eAIlB,GAFW,OAAPuD,IAA+B,OAAhBA,GAAGrE,UAAmBqE,GAAGrE,SAAS8C,KAAKpD,OAAQ0E,KAEvD,OAAPG,GAAa,CACf,KAAqB,OAAdA,GAAGtE,QAAmBsE,GAAGtE,OAAO1S,OAAS,GAAGgX,GAAGtE,OAAO5F,QAAQjB,SAAQ,GACzD,OAAhBmL,GAAGvE,UAAmBuE,GAAGvE,SAAS8C,KAAKpD,OAAQ0E,MAIvD,SAASjE,WAAYiE,KACnB,MAAM1E,OAASxY,KAAKwY,OAEhB0E,KAAK1E,OAAO5O,QAAQsT,KACxB1E,OAAOa,cA3cmBG,UA6cN,OAAhBxZ,KAAK+Y,QAiCX,SAASuE,WAAYvE,QACnB,IAAK,IAAIrI,EAAI,EAAGA,EAAIqI,OAAO1S,OAAQqK;;AAEN,KAArBqI,OAAOrI,GAAG6M,SACdxE,OAAO5F,QAAQjB,SAAQ,GACvBxB,KAtCsB4M,CAAWtd,KAAK+Y,QArdnB,UA2CEQ,QA4apBf,OAAOa,gBACVb,OAAOa,cA7ciBG,UATH,WAAA,SAudhBhB,OAAOa,eACVb,OAAOpP,KAAK,UAIhBpJ,KAAK8a,iBAGP,SAASQ,UAAW4B,KACdA,KAAKld,KAAKwY,OAAO5O,QAAQsT,KAC7Bld,KAAKwY,OAAOa,cArfsBG,WAsfX,IAAnBxZ,KAAKob,WAAqE,IAxgBlD,IAwgBKpb,KAAKwY,OAAOa,gBAAoCrZ,KAAKwY,OAAOa,cA5e3DG,WA6elCxZ,KAAK8a,iBAGP,SAASS,eAC4C,IAhhBvB,GAghBvBvb,KAAKwY,OAAOa,gBACfrZ,KAAKwY,OAAOa,cApfoBG,UAqfhCxZ,KAAK+Z,UAIT,SAASZ,gBAC6C,IAnf7B,OAmflBnZ,KAAKwY,OAAOa,gBACfrZ,KAAKwY,OAAOa,cApeYG,UAqexBxZ,KAAK+Z,UAcT,SAASW,UAAWwC,KAClB,MAAM1E,OAASxY,KAAKwY,OAEhB0E,KAAK1E,OAAO5O,QAAQsT,KAEmB,IApjBvB,EAojBf1E,OAAOa,gBAC0C,IAxe5BM,MAwenBnB,OAAOa,gBAA2Cb,OAAOa,cA5iBpC,IA6iB2B,IA/d5BM,UA+dpBnB,OAAOa,gBAA4Cb,OAAOa,cAzgB1C,SA0gBrBb,OAAOpP,KAAK,SAGdoP,OAAOa,cAxfUG,UA0fa,OAA1BhB,OAAOoB,gBACTpB,OAAOoB,eAAekB,iBAGM,OAA1BtC,OAAO4E,gBACT5E,OAAO4E,eAAetC,iBAI1B,SAAS8B,eAAgBM,IAAKrO,MACxBA,MAAAA,MAAqC7O,KAAK+E,KAAK8J,MACnD7O,KAAK4Z,eAAeX,WAAWiE,KAGjC,SAASM,YAAajd,MACQ,OAAxBP,KAAKod,iBACM,SAAT7c,OACFP,KAAKqZ,qBACLrZ,KAAKod,eAAepC,kBAET,aAATza,OACFP,KAAKqZ,cAlkBmB,KAmkBxBrZ,KAAKod,eAAepC,mBAII,OAAxBhb,KAAK4Z,gBACM,UAATrZ,OACFP,KAAKqZ,cAviBc,SAwiBnBrZ,KAAK4Z,eAAeoB,kBAK1B,MAAMyC,eAAehG,aACnB5X,YAAa6d,MACXC,QAEA3d,KAAKqZ,aAAe,EACpBrZ,KAAKod,eAAiB,KACtBpd,KAAK4Z,eAAiB,KAElB8D,OACEA,KAAKE,OAAM5d,KAAKya,MAAQiD,KAAKE,MAC7BF,KAAK9T,UAAS5J,KAAK4a,SAAW8C,KAAK9T,SACnC8T,KAAKG,aAAY7d,KAAK8d,YAAcJ,KAAKG,YACzCH,KAAKK,QACPL,KAAKK,OAAOC,iBAAiB,QAAStP,MAAM/J,KAAK3E,QAIrDA,KAAK2K,GAAG,cAAe6S,aAGzB/C,MAAOf,IACLA,GAAG,MAGLkB,SAAUlB,IACRA,GAAG,MAGLoE;;CAIIG,eACF,OAA+B,OAAxBje,KAAKod,qBAAiCnP,EAG3CrB,eACF,OAA+B,OAAxB5M,KAAK4Z,qBAAiC3L,EAG3CiQ,gBACF,OAA2C,IAtoBzB,EAsoBVle,KAAKqZ,cAGX8E,iBACF,OAAgD,IAvkB7B5D,GAukBXva,KAAKqZ,cAGfzP,QAASsT,KACsC,IA3kB1B3C,GA2kBdva,KAAKqZ,gBACH6D,MAAKA,IAAMxF,kBAChB1X,KAAKqZ,aA1kBS+E,WAvEE,EAipBKpe,KAAKqZ,cAEE,OAAxBrZ,KAAKod,iBACPpd,KAAKod,eAAe3E,cAAgB,EACpCzY,KAAKod,eAAezV,MAAQuV,KAEF,OAAxBld,KAAK4Z,iBACP5Z,KAAK4Z,eAAenB,cAAgB,EACpCzY,KAAK4Z,eAAejS,MAAQuV,KAG9Bld,KAAKqZ,cA7pBW,EA8pBhBrZ,KAAK8d,cACL9d,KAAKqZ,cA1pBeG,UA4pBQ,OAAxBxZ,KAAKod,gBAAyBpd,KAAKod,eAAepC,iBAC1B,OAAxBhb,KAAK4Z,gBAAyB5Z,KAAK4Z,eAAeoB,mBAK5D,MAAMqD,mBAAiBZ,OACrB5d,YAAa6d,MACXC,MAAMD,MAEN1d,KAAKqZ,cAAgBiF,QACrBte,KAAKod,eAAiB,IAAInC,cAAcjb,KAAM0d,MAE1CA,QACoC,IAAlC1d,KAAKod,eAAehC,YAAqBpb,KAAKqZ,cAvoBpBG,WAwoB1BkE,KAAKzB,OAAMjc,KAAKoc,MAAQsB,KAAKzB,MAC7ByB,KAAKa,WAAWve,KAAKod,eAAepC,iBACpC0C,KAAK5O,UAAU9O,KAAKwe,YAAYd,KAAK5O,WAI7C0P,YAAa1P,UACX,MAAM2P,IAAM,IAAI5G,YAAY/I,UACtB7N,IAAMjB,KAAKod,eAAenc,KAAOyd,OAEvC,OADA1e,KAAKod,eAAenc,IAGpB,SAAS0d,UAAW9P,MAClB,MAAMmE,KAAOyL,IAAI1Z,KAAK8J,MACtB,MAAgB,KAATmE,OAAoC,IAApBnE,KAAKmF,YAAoByK,IAAIhI,UAAY,GAAK,KAAOxV,IAAI+R,OAJ3EhT,KAQToc,MAAO1C,IACLA,GAAG,MAGL8B,KAAMoD,KAAMlF,IAGV,OAFA1Z,KAAKod,eAAepC,iBACpBhb,KAAKod,eAAe5B,KAAKoD,KAAMlF,IACxBkF,KAGT3C,OAEE,OADAjc,KAAKod,eAAepC,iBACbhb,KAAKod,eAAenB,OAG7BlX,KAAM8J,MAEJ,OADA7O,KAAKod,eAAeX,uBACbzc,KAAKod,eAAerY,KAAK8J,MAGlCkN,QAASlN,MAEP,OADA7O,KAAKod,eAAeX,uBACbzc,KAAKod,eAAerB,QAAQlN,MAGrCgQ,SAGE,OAFA7e,KAAKqZ,cAhsBuB8C,OAisB5Bnc,KAAKod,eAAepC,iBACbhb,KAGT8e,QAEE,OADA9e,KAAKqZ,eAAmD,IAAlCrZ,KAAKod,eAAehC,UAzrBV5B,UAPAA,UAisBzBxZ,KAGT+e,0BAA2BC,IAAKtB,MAC9B,IAAI9T,QAEJ,MAAMuT,GAAK,IAAIkB,WAAS,IACnBX,KACHzB,KAAMvC,IACJsF,IAAIhM,OAAOiM,KAAKla,MAAMka,KAAKvF,GAAG/U,KAAK,KAAM,OAAOua,MAAMxF,KAExDmE,aACEjU,QAAUoV,IAAIG,UAEhBvV,QAAS8P,IACP,IAAK9P,QAAS,OAAO8P,GAAG,MACxB9P,QAAQqV,KAAKvF,GAAG/U,KAAK,KAAM,OAAOua,MAAMxF,OAI5C,OAAOyD,GAEP,SAASpY,KAAM8J,MACTA,KAAK+M,KAAMuB,GAAGpY,KAAK,MAClBoY,GAAGpY,KAAK8J,KAAK9C,QAItBgT,YAAalQ,KAAM6O,MACjB,GAoWJ,SAAS0B,cAAe5G,QACtB,OAAOkD,UAAUlD,SAAWA,OAAOyF,SArW7BmB,CAAcvQ,MAAO,OAAOA,KAChC,GAAIA,KAAKwJ,eAAgB,OAAOrY,KAAKqf,mBAAmBxQ,KAAKwJ,iBAAkBqF,MAC1Exa,MAAMsB,QAAQqK,QAAOA,UAAgBZ,IAATY,KAAqB,GAAK,CAACA,OAE5D,IAAI6B,EAAI,EACR,OAAO,IAAI2N,WAAS,IACfX,KACHzB,KAAMvC,IACJ1Z,KAAK+E,KAAK2L,IAAM7B,KAAKxI,OAAS,KAAOwI,KAAK6B,MAC1CgJ,GAAG,SAKTqF,uBAAwB5B,IACtB,OAAwD,IA5rB3BtD,MA4rBrBsD,GAAG9D,eAAkD8D,GAAGC,eAAevE,UAAYsE,GAAGC,eAAe3E,cAG/GsG,gBAAiB5B,IACf,OAA4C,IAvwBlB,IAuwBlBA,GAAG9D,cAGbhB,CAACA,iBACC,MAAMG,OAASxY,KAEf,IAAI2H,MAAQ,KACR2X,eAAiB,KACjBC,cAAgB,KAMpB,OAJAvf,KAAK2K,GAAG,SAAUuS,MAAUvV,MAAQuV,OACpCld,KAAK2K,GAAG,YAwBR,SAAS6U,aACgB,OAAnBF,gBAAyBG,OAAOjH,OAAOyD,WAxB7Cjc,KAAK2K,GAAG,SA2BR,SAASkR,UACgB,OAAnByD,gBAAyBG,OAAO,SA1B/B,CACLpH,CAACA,iBACC,OAAOrY,MAETgT,OACE,OAAO,IAAI0M,SAAQ,SAAUxN,QAASyN,QACpCL,eAAiBpN,QACjBqN,cAAgBI,OAChB,MAAM9Q,KAAO2J,OAAOyD,OACP,OAATpN,KAAe4Q,OAAO5Q,MACqB,IAzyBnC,EAyyBF2J,OAAOa,eAAiCoG,OAAO,UAG7DN,SACE,OAAOvV,QAAQ,OAEjBgW,MAAO1C,KACL,OAAOtT,QAAQsT,OAYnB,SAASuC,OAAQ5Q,MACO,OAAlB0Q,gBACA5X,MAAO4X,cAAc5X,OACP,OAATkH,MAAuD,IA/yBxC,MA+yBG2J,OAAOa,cAAiCkG,cAAc7H,kBAC5E4H,eAAe,CAAEvT,MAAO8C,KAAM+M,KAAe,OAAT/M,OACzC0Q,cAAgBD,eAAiB,MAGnC,SAAS1V,QAASsT,KAEhB,OADA1E,OAAO5O,QAAQsT,KACR,IAAIwC,SAAQ,CAACxN,QAASyN,UAC3B,GAv0Bc,EAu0BVnH,OAAOa,aAA0B,OAAOnH,QAAQ,CAAEnG,WAAOkC,EAAW2N,MAAM,IAC9EpD,OAAO5I,KAAK,SAAS,WACfsN,IAAKyC,OAAOzC,KACXhL,QAAQ,CAAEnG,WAAOkC,EAAW2N,MAAM,YAOjD,MAAMtU,mBAAiBmW,OACrB5d,YAAa6d,MACXC,MAAMD,MAEN1d,KAAKqZ,cAAgBiF,MACrBte,KAAK4Z,eAAiB,IAAIrB,cAAcvY,KAAM0d,MAE1CA,OACEA,KAAKmC,SAAQ7f,KAAK8Z,QAAU4D,KAAKmC,QACjCnC,KAAK9O,QAAO5O,KAAKia,OAASyD,KAAK9O,OAC/B8O,KAAKoC,QAAO9f,KAAKqa,OAASqD,KAAKoC,OAC/BpC,KAAKa,WAAWve,KAAK4Z,eAAeoB,kBAI5C+E,OACE/f,KAAKqZ,cA7yBgB,UAgzBvB2G,SACEhgB,KAAKqZ,cAxyBmBG,UAyyBxBxZ,KAAK4Z,eAAeoB,iBAGtBlB,QAASmG,MAAOvG,IACdA,GAAG,MAGLO,OAAQpL,KAAM6K,IACZ1Z,KAAK4Z,eAAeH,UAAU5K,KAAM6K,IAGtCW,OAAQX,IACNA,GAAG,MAGLqF,uBAAwB1B,IACtB,OAAyD,IAxxB3B6C,UAwxBtB7C,GAAGhE,cAGb0F,eAAgB1B,IACd,GAAIA,GAAGa,UAAW,OAAOwB,QAAQxN,SAAQ,GACzC,MAAMiO,MAAQ9C,GAAGzD,eAEX2D,QA2PV,SAAS6C,SAAUC,GACjB,OAAOA,EAAEvG,UAAYxS,WAASuC,UAAUiQ,SAAWuG,EAAEvG,UAAYwG,OAAOzW,UAAUiQ,QA7P/DsG,CAAS/C,IAAMlG,KAAKoJ,IAAI,EAAGJ,MAAMvH,MAAMvS,QAAU8Z,MAAMvH,MAAMvS,SA10BzD,SA20BMgX,GAAGhE,aAAgC,EAAI,GAClE,OAAe,IAAXkE,OAAqBmC,QAAQxN,SAAQ,IACpB,OAAjBiO,MAAMpH,SAAiBoH,MAAMpH,OAAS,IACnC,IAAI2G,SAASxN,UAClBiO,MAAMpH,OAAOhU,KAAK,CAAEwY,OAAAA,OAAQrL,QAAAA,cAIhCtD,MAAOC,MAEL,OADA7O,KAAK4Z,eAAeoB,iBACbhb,KAAK4Z,eAAe7U,KAAK8J,MAGlC/B,IAAK+B,MAGH,OAFA7O,KAAK4Z,eAAeoB,iBACpBhb,KAAK4Z,eAAe9M,IAAI+B,MACjB7O,MAIX,MAAMsgB,eAAejC;AACnBxe,YAAa6d,MACXC,MAAMD,MAEN1d,KAAKqZ,aAx5Ba,EAsBQ,OAk4BKrZ,KAAKqZ,aACpCrZ,KAAK4Z,eAAiB,IAAIrB,cAAcvY,KAAM0d,MAE1CA,OACEA,KAAKmC,SAAQ7f,KAAK8Z,QAAU4D,KAAKmC,QACjCnC,KAAK9O,QAAO5O,KAAKia,OAASyD,KAAK9O,OAC/B8O,KAAKoC,QAAO9f,KAAKqa,OAASqD,KAAKoC,QAIvCC,OACE/f,KAAKqZ,cA52BgB,UA+2BvB2G,SACEhgB,KAAKqZ,cAv2BmBG,UAw2BxBxZ,KAAK4Z,eAAeoB,iBAGtBlB,QAASmG,MAAOvG,IACdA,GAAG,MAGLO,OAAQpL,KAAM6K,IACZ1Z,KAAK4Z,eAAeH,UAAU5K,KAAM6K,IAGtCW,OAAQX,IACNA,GAAG,MAGL9K,MAAOC,MAEL,OADA7O,KAAK4Z,eAAeoB,iBACbhb,KAAK4Z,eAAe7U,KAAK8J,MAGlC/B,IAAK+B,MAGH,OAFA7O,KAAK4Z,eAAeoB,iBACpBhb,KAAK4Z,eAAe9M,IAAI+B,MACjB7O,MAIX,MAAMwgB,kBAAkBF,OACtBzgB,YAAa6d,MACXC,MAAMD,MACN1d,KAAKygB,gBAAkB,IAAI9D,eAAe3c,MAEtC0d,OACEA,KAAKgD,YAAW1gB,KAAK2gB,WAAajD,KAAKgD,WACvChD,KAAK/G,QAAO3W,KAAK4gB,OAASlD,KAAK/G,QAIvCsD,OAAQpL,KAAM6K,IACR1Z,KAAKod,eAAevE,UAAY7Y,KAAKod,eAAe3E,cACtDzY,KAAKygB,gBAAgB5R,KAAOA,KAE5B7O,KAAK2gB,WAAW9R,KAAM7O,KAAKygB,gBAAgB7D,gBAI/CR,MAAO1C,IACL,GAAkC,OAA9B1Z,KAAKygB,gBAAgB5R,KAAe,CACtC,MAAMA,KAAO7O,KAAKygB,gBAAgB5R,KAClC7O,KAAKygB,gBAAgB5R,KAAO,KAC5B6K,GAAG,MACH1Z,KAAK2gB,WAAW9R,KAAM7O,KAAKygB,gBAAgB7D,qBAE3ClD,GAAG,MAIP9P,QAASsT,KACPS,MAAM/T,QAAQsT,KACoB,OAA9Bld,KAAKygB,gBAAgB5R,OACvB7O,KAAKygB,gBAAgB5R,KAAO,KAC5B7O,KAAKygB,gBAAgB7D,kBAIzB+D,WAAY9R,KAAM6K,IAChBA,GAAG,KAAM7K,MAGX+R,OAAQlH,IACNA,GAAG,MAGLW,OAAQX,IACN1Z,KAAKygB,gBAAgBnG,WAAaZ,GAClC1Z,KAAK4gB,OAAOC,oBAAoBlc,KAAK3E,QAMzC,SAAS6gB,oBAAqB3D,IAAKrO,MACjC,MAAM6K,GAAK1Z,KAAKygB,gBAAgBnG,WAChC,GAAI4C,IAAK,OAAOxD,GAAGwD,KACfrO,MAAAA,MAAqC7O,KAAK+E,KAAK8J,MACnD7O,KAAK+E,KAAK,MACV2U,GAAG,MAYL,SAASZ,SAAUN,UAAWsI,SAC5B,MAAMC,IAAM7d,MAAMsB,QAAQgU,QAAU,IAAIA,UAAWsI,SAAW,CAACtI,UAAWsI,SACpElF,KAAQmF,IAAI1a,QAAyC,mBAAxB0a,IAAIA,IAAI1a,OAAS,GAAqB0a,IAAIC,MAAQ,KAErF,GAAID,IAAI1a,OAAS,EAAG,MAAM,IAAIO,MAAM,wCAEpC,IAAIiW,IAAMkE,IAAI,GACVnC,KAAO,KACPjX,MAAQ,KAEZ,IAAK,IAAI+I,EAAI,EAAGA,EAAIqQ,IAAI1a,OAAQqK,IAC9BkO,KAAOmC,IAAIrQ,GAEPgL,UAAUmB,KACZA,IAAIrB,KAAKoD,KAAMjD,UAEfsF,YAAYpE,KAAK,EAAMnM,EAAI,EAAGiL,SAC9BkB,IAAIrB,KAAKoD,OAGX/B,IAAM+B,KAGR,GAAIhD,KAAM,CACR,IAAIsF,KAAM,EAEV,MAAMC,YAAczF,UAAUkD,UAAYA,KAAKhF,iBAAkBgF,KAAKhF,eAAeuH,aAErFvC,KAAKjU,GAAG,SAAUuS,MACF,OAAVvV,QAAgBA,MAAQuV,QAG9B0B,KAAKjU,GAAG,UAAU,KAChBuW,KAAM,EACDC,aAAavF,KAAKjU,UAGrBwZ,aACFvC,KAAKjU,GAAG,SAAS,IAAMiR,KAAKjU,QAAUuZ,IAAM,KAAOvJ,oBAIvD,OAAOiH,KAEP,SAASqC,YAAaZ,EAAGe,GAAIC,GAAI1F,SAC/B0E,EAAE1V,GAAG,QAASgR,SACd0E,EAAE1V,GAAG,SAEL,SAASkR,UACP,GAAIuF,IAAMf,EAAEjD,iBAAmBiD,EAAEjD,eAAehE,MAAO,OAAOuC,QAAQhE,iBACtE,GAAI0J,IAAMhB,EAAEzG,iBAAmByG,EAAEzG,eAAeR,MAAO,OAAOuC,QAAQhE,oBAI1E,SAASgE,QAASuB,KAChB,GAAKA,MAAOvV,MAAZ,CACAA,MAAQuV,IAER,IAAK,MAAMmD,KAAKU,IACdV,EAAEzW,QAAQsT,OAKhB,SAASwB,OAAM2B,GACb,OAAOA,EAGT,SAASiB,SAAU9I,QACjB,QAASA,OAAO4E,kBAAoB5E,OAAOoB,eAG7C,SAAS8B,UAAWlD,QAClB,MAAsC,iBAAxBA,OAAOa,cAA6BiI,SAAS9I,QA8B7D,SAASQ,kBAAmBnK,MAC1B,OALF,SAAS0S,aAAc1S,MACrB,MAAuB,iBAATA,MAA8B,OAATA,MAA4C,iBAApBA,KAAKmF,WAIzDuN,CAAa1S,MAAQA,KAAKmF,WAAa,KAGhD,SAASlK,UAET,SAAS4E,QACP1O,KAAK4J,QAAQ,IAAIhD,MAAM,wBAOzB4a,QAAiB,CACf1I,SAAAA,SACA2I,gBAhIF,SAASA,mBAAoBX,SAC3B,OAAO,IAAIpB,SAAQ,CAACxN,QAASyN,SACpB7G,YAAYgI,SAAU5D,MAC3B,GAAIA,IAAK,OAAOyC,OAAOzC,KACvBhL,gBA6HJoP,SAAAA,SACA5F,UAAAA,UACAgG,QA9CF,SAASA,QAASlJ,QAChB,QAASA,OAAO4E,gBAAkB5E,OAAO4E,eAAehE,OA8CxDuI,WA3CF,SAASA,WAAYnJ,QACnB,QAASA,OAAOoB,gBAAkBpB,OAAOoB,eAAeR,OA2CxDwI,YA7BF,SAASA,YAAapJ,QACpB,OA1mCoB,IAAA,EA0mCZA,OAAOa,eAAqF,IAhiC5EsB,SAgiC+BnC,OAAOa,8BAZhE,SAASwI,iBAAgBrJ,OAAQkF,KAAO,IACtC,MAAMR,IAAO1E,OAAO4E,gBAAkB5E,OAAO4E,eAAezV,OAAW6Q,OAAOoB,gBAAkBpB,OAAOoB,eAAejS;mCAGtH;OAAS+V,KAAKqD,KAAO7D,MAAQxF,iBAA2BwF,IAAP,MAsCjDO,OAAAA,gBACAnW,oBACA+W,WACAiC,OAAAA,OACAE,UAAAA;;AAEAsB,YAvJF,MAAMA,oBAAoBtB,0BCvgC1B,MAAMvM,MAAMjN,MAIN+a,YAAc,IAAIC,WAAW,GAC7BC,YAAchO,MAAI5Q,KAAK,CAAC,IAAM,IAAM,IAAM,GAAM,IAAM,IACtD6e,UAAYjO,MAAI5Q,KAAK,CAAC0e,YAAaA,cACnCI,UAAYlO,MAAI5Q,KAAK,CAAC,IAAM,IAAM,IAAM,GAAM,IAAM,KACpD+e,QAAUnO,MAAI5Q,KAAK,CAAC,GAAM,IA0NhC,SAAS8M,QAASkS,MAAOC,IAAKtN,OAAQlI,KACpC,KAAOkI,OAASlI,IAAKkI,SACnB,GAAIqN,MAAMrN,UAAYsN,IAAK,OAAOtN,OAEpC,OAAOlI,IAGT,SAASyV,MAAOF,OACd,IAAIG,IAAM,IACV,IAAK,IAAI9R,EAAI,EAAGA,EAAI,IAAKA,IAAK8R,KAAOH,MAAM3R,GAC3C,IAAK,IAAI+R,EAAI,IAAKA,EAAI,IAAKA,IAAKD,KAAOH,MAAMI,GAC7C,OAAOD,IAGT,SAASE,UAAW/O,IAAK0D,GAEvB,OADA1D,IAAMA,IAAIxT,SAAS,IACXkG,OAASgR,EA/OJ,sBA+OqB9J,MAAM,EAAG8J,GAAK,IAhPpC,sBAiPC9J,MAAM,EAAG8J,EAAI1D,IAAItN,QAAUsN,IAAM,IAkDhD,SAASgP,UAAWhP,IAAKqB,OAAQ3O;;AAK/B,GAAkB,KAJlBsN,IAAMA,IAAIiP,SAAS5N,OAAQA,OAAS3O,SACpC2O,OAAS;;;;;;AAIP,OAhCJ,SAAS6N,SAAUC;;;AAGjB,IAAIC,SACJ,GAAe,MAAXD,IAAI,GAAaC,UAAW,MAC3B,CAAA,GAAe,MAAXD,IAAI,GACR,OAAO;8DADcC;UAAW,EAIrC,MAAMC,MAAQ,GACd,IAAItS,EACJ,IAAKA,EAAIoS,IAAIzc,OAAS,EAAGqK,EAAI,EAAGA,IAAK,CACnC,MAAM4G,KAAOwL,IAAIpS,GACbqS,SAAUC,MAAMje,KAAKuS,MACpB0L,MAAMje,KAAK,IAAOuS,MAGzB,IAAIkL,IAAM,EACV,MAAMS,EAAID,MAAM3c,OAChB,IAAKqK,EAAI,EAAGA,EAAIuS,EAAGvS,IACjB8R,KAAOQ,MAAMtS,GAAKyG,KAAK+L,IAAI,IAAKxS,GAGlC,OAAOqS,SAAWP,KAAO,EAAIA,IASpBK,CAASlP,KACX;;AAEL,KAAOqB,OAASrB,IAAItN,QAA0B,KAAhBsN,IAAIqB,SAAgBA,SAClD,MAAMlI,IAhJV,SAASqW,MAAOC,MAAOC,IAAKC,cAC1B,MAAqB,iBAAVF,MAA2BE;CACtCF,QAAUA,QACGC,IAAYA,IACrBD,OAAS,IACbA,OAASC,MACI,EAFUD,MAGhB,EAyIOD,CAAMhT,QAAQwD,IAAK,GAAIqB,OAAQrB,IAAItN,QAASsN,IAAItN,OAAQsN,IAAItN,QACxE,KAAO2O,OAASlI,KAAuB,IAAhB6G,IAAIqB,SAAeA,SAC1C,OAAIlI,MAAQkI,OAAe,EACpBuO,SAAStP,MAAI9T,SAASwT,IAAIiP,SAAS5N,OAAQlI,MAAO,IAI7D,SAAS0W,UAAW7P,IAAKqB,OAAQ3O,OAAQyI,UACvC,OAAOmF,MAAI9T,SAASwT,IAAIiP,SAAS5N,OAAQ7E,QAAQwD,IAAK,EAAGqB,OAAQA,OAAS3O,SAAUyI,UAGtF,SAAS2U,UAAWC,KAClB,MAAML,IAAMpP,MAAID,WAAW0P,KAC3B,IAAIC,OAASxM,KAAKyM,MAAMzM,KAAK0M,IAAIR,KAAOlM,KAAK0M,IAAI,KAAO,EAGxD,OAFIR,IAAMM,QAAUxM,KAAK+L,IAAI,GAAIS,SAASA,SAElCN,IAAMM,OAAUD,6BAlTD,SAASI,eAAgBhB,IAAKhU,UACrD,OAAO0U,UAAUV,IAAK,EAAGA,IAAIzc,OAAQyI,+BAGnB,SAASiV,UAAWrG;AACtC,IAAItX,OAAS,GACTsX,KAAKnd,OAAM6F,QAAUqd,UAAU,SAAW/F,KAAKnd,KAAO,OACtDmd,KAAKsG,WAAU5d,QAAUqd,UAAU,aAAe/F,KAAKsG,SAAW,OACtE,MAAMC,IAAMvG,KAAKuG,IACjB,GAAIA,IACF,IAAK,MAAM5W,OAAO4W,IAChB7d,QAAUqd,UAAU,IAAMpW,IAAM,IAAM4W,IAAI5W,KAAO,MAGrD,OAAO4G,MAAI5Q,KAAK+C,6BAGE,SAAS8d,UAAWpB,KACtC,MAAM1c,OAAS,GAEf,KAAO0c,IAAIzc,QAAQ,CACjB,IAAIqK,EAAI,EACR,KAAOA,EAAIoS,IAAIzc,QAAqB,KAAXyc,IAAIpS,IAAWA,IACxC,MAAM2S,IAAME,SAAStP,MAAI9T,SAAS2iB,IAAIF,SAAS,EAAGlS,IAAK,IACvD,IAAK2S,IAAK,OAAOjd,OAEjB,MAAM2J,EAAIkE,MAAI9T,SAAS2iB,IAAIF,SAASlS,EAAI,EAAG2S,IAAM,IAC3Cc,SAAWpU,EAAEI,QAAQ,KAC3B,IAAkB,IAAdgU,SAAiB,OAAO/d,OAC5BA,OAAO2J,EAAExC,MAAM,EAAG4W,WAAapU,EAAExC,MAAM4W,SAAW,GAElDrB,IAAMA,IAAIF,SAASS,KAGrB,OAAOjd,yBAGQ,SAASge,OAAQ1G,MAChC,MAAMoF,IAAM7O,MAAIG,MAAM,KACtB,IAAI7T,KAAOmd,KAAKnd,KACZ8jB,OAAS,GAGb,GADsB,IAAlB3G,KAAK4G,UAA4C,MAA1B/jB,KAAKA,KAAK8F,OAAS,KAAY9F,MAAQ,KAC9D0T,MAAID,WAAWzT,QAAUA,KAAK8F,OAAQ,OAAO,aAEjD;KAAO4N,MAAID,WAAWzT,MAAQ,KAAK,CACjC,MAAMmQ,EAAInQ,KAAK4P,QAAQ,KACvB,IAAW,IAAPO,EAAU,OAAO,KACrB2T,QAAUA,OAAS,IAAM9jB,KAAKgN,MAAM,EAAGmD,GAAKnQ,KAAKgN,MAAM,EAAGmD,GAC1DnQ,KAAOA,KAAKgN,MAAMmD,EAAI,GAGxB,OAAIuD,MAAID,WAAWzT,MAAQ,KAAO0T,MAAID,WAAWqQ,QAAU,KACvD3G,KAAKsG,UAAY/P,MAAID,WAAW0J,KAAKsG,UAAY,IADkB,MAGvE/P,MAAIrF,MAAMkU,IAAKviB,MACf0T,MAAIrF,MAAMkU,IAAKJ,UA5DJ,KA4DchF,KAAK6G,KAAa,GAAI,KAC/CtQ,MAAIrF,MAAMkU,IAAKJ,UAAUhF,KAAK8G,IAAK,GAAI,KACvCvQ,MAAIrF,MAAMkU,IAAKJ,UAAUhF,KAAK+G,IAAK,GAAI,KAuLzC,SAASC,WAAYpC,IAAKQ,IAAK6B,KACzBrC,IAAIniB,SAAS,GAAGkG,OAAS,GAT/B,SAASue,cAAetC,IAAKQ,IAAK6B,KAChC7B,IAAI6B,KAAO,IACX,IAAK,IAAIjU,EAAI,GAAIA,EAAI,EAAGA,IACtBoS,IAAI6B,IAAMjU,GAAW,IAAN4R,IACfA,IAAMnL,KAAKyM,MAAMtB,IAAM,KAMvBsC,CAActC,IAAKQ,IAAK6B,KAExB1Q,MAAIrF,MAAMkU,IAAKJ,UAAUJ,IAAK,IAAKqC,KA1LrCD,CAAWhH,KAAKrJ,KAAMyO,IAAK,KAC3B7O,MAAIrF,MAAMkU,IAAKJ,UAAWhF,KAAKmH,MAAMC,UAAY,IAAQ,EAAG,IAAK,KAEjEhC,IAAI,KAAOf,YA8Hb,SAASgD,WAAYC,MACnB,OAAQA,MACN,IAAK,OACH,OAAO,EACT,IAAK,OACH,OAAO,EACT,IAAK,UACH,OAAO,EACT,IAAK,mBACH,OAAO,EACT,IAAK,eACH,OAAO,EACT,IAAK,YACH,OAAO,EACT,IAAK,OACH,OAAO,EACT,IAAK,kBACH,OAAO,EACT,IAAK,aACH,OAAO,GAGX,OAAO,EApJkBD,CAAWrH,KAAK5d,MAErC4d,KAAKsG,UAAU/P,MAAIrF,MAAMkU,IAAKpF,KAAKsG,SAAU,KAEjD/P,MAAIU,KAAKsN,YAAaa,IArEH,KAsEnB7O,MAAIU,KAAKuN,UAAWY,IArEC,KAsEjBpF,KAAKuH,OAAOhR,MAAIrF,MAAMkU,IAAKpF,KAAKuH,MAAO,KACvCvH,KAAKwH,OAAOjR,MAAIrF,MAAMkU,IAAKpF,KAAKwH,MAAO,KAC3CjR,MAAIrF,MAAMkU,IAAKJ,UAAUhF,KAAKyH,UAAY,EAAG,GAAI,KACjDlR,MAAIrF,MAAMkU,IAAKJ,UAAUhF,KAAK0H,UAAY,EAAG,GAAI,KAE7Cf,QAAQpQ,MAAIrF,MAAMkU,IAAKuB,OAAQ,KAEnCpQ,MAAIrF,MAAMkU,IAAKJ,UAAUH,MAAMO,KAAM,GAAI,KAElCA,uBAGQ,SAASpM,OAAQoM,IAAKuC,iBAAkBC,oBACvD,IAAIhB,SAAwB,IAAbxB,IAAI,KAAa,EAAIA,IAAI,KAAOf,YAE3CxhB,KAAOijB,UAAUV,IAAK,EAAG,IAAKuC,kBAClC,MAAMd,KAAO5B,UAAUG,IAAK,IAAK,GAC3B0B,IAAM7B,UAAUG,IAAK,IAAK,GAC1B2B,IAAM9B,UAAUG,IAAK,IAAK,GAC1BzO,KAAOsO,UAAUG,IAAK,IAAK,IAC3B+B,MAAQlC,UAAUG,IAAK,IAAK,IAC5BhjB,KAmER,SAASylB,OAAQP,MACf,OAAQA,MACN,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,UACT,KAAK,EACH,MAAO,mBACT,KAAK,EACH,MAAO,eACT,KAAK,EACH,MAAO,YACT,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,kBACT,KAAK,GACH,MAAO,aACT,KAAK,GACH,MAAO,oBACT,KAAK,GACH,MAAO,qBACT,KAAK,GACL,KAAK,GACH,MAAO,gBAGX,OAAO,KAhGMO,CAAOjB,UACdN,SAAwB,IAAblB,IAAI,KAAa,KAAOU,UAAUV,IAAK,IAAK,IAAKuC,kBAC5DJ,MAAQzB,UAAUV,IAAK,IAAK,IAC5BoC,MAAQ1B,UAAUV,IAAK,IAAK,IAC5BqC,SAAWxC,UAAUG,IAAK,IAAK,GAC/BsC,SAAWzC,UAAUG,IAAK,IAAK,GAE/B0C,EAAIjD,MAAMO;;AAGhB,GAAU,MAAN0C,EAAc,OAAO;iBAGzB;GAAIA,IAAM7C,UAAUG,IAAK,IAAK,GAAI,MAAM,IAAIlc,MAAM,+EAElD,GAiCF,SAAS6e,QAAS3C,KAChB,OAAO7O,MAAIc,OAAOkN,YAAaa,IAAIF,SA7IhB,IA6IuC8C,MAlCtDD,CAAQ3C;;;AAGNA,IAAI,OAAMviB,KAAOijB,UAAUV,IAAK,IAAK,IAAKuC,kBAAoB,IAAM9kB,WACnE,GAiCT,SAASolB,MAAO7C,KACd,OAAO7O,MAAIc,OAAOoN,UAAWW,IAAIF,SAjJd,IAiJqC8C,OACtDzR,MAAIc,OAAOqN,QAASU,IAAIF,SAjJL,IAiJ8BgD,MAnCxCD,CAAM7C,WAIf,IAAKwC,mBACH,MAAM,IAAI1e,MAAM;mEAOpB;OAFiB,IAAb0d,UAAkB/jB,MAAkC,MAA1BA,KAAKA,KAAK8F,OAAS,KAAYie,SAAW,GAEjE,CACL/jB,KAAAA,KACAgkB,KAAAA,KACAC,IAAAA,IACAC,IAAAA,IACApQ,KAAAA,KACAwQ,MAAO,IAAIhhB,KAAK,IAAOghB,OACvB/kB,KAAAA,KACAkkB,SAAAA,SACAiB,MAAAA,MACAC,MAAAA,MACAC,SAAAA,SACAC,SAAAA,SACAnB,IAAK,OClJT,eAAQ3c,oBAAU+W,0BAAUwD,kBAAmB7a,QACzC4Q,KAAOzQ,SACP8M,MAAM5M,MACNyG,UAAUvG,UAEVse,MAAQ5R,MAAIG,MAAM,GAExB,MAAM0R,WACJjmB,cACEG,KAAK6Y,SAAW,EAChB7Y,KAAK+lB,QAAU,EACf/lB,KAAK4Y,MAAQ,IAAIhB,KAEjB5X,KAAKgmB,QAAU,EAGjBjhB,KAAM8L,QACJ7Q,KAAK6Y,UAAYhI,OAAOmD,WACxBhU,KAAK4Y,MAAM7T,KAAK8L,QAGlBoV,WAAY5R,MACV,OAA0B,IAAnBrU,KAAKkmB,UAAkB,KAAOlmB,KAAKmmB,MAAM9R,MAGlDlB,MAAOkB,MACL,GAAIA,KAAOrU,KAAK6Y,SAAU,OAAO,KACjC,GAAa,IAATxE,KAAY,OAAOwR,MAEvB,IAAIO,MAAQpmB,KAAKmmB,MAAM9R,MAEvB,GAAIA,OAAS+R,MAAMpS,WAAY,OAAOoS,oBAEtC;MAAMC,OAAS,CAACD,OAEhB,MAAQ/R,MAAQ+R,MAAMpS,YAAc,GAClCoS,MAAQpmB,KAAKmmB,MAAM9R,MACnBgS,OAAOthB,KAAKqhB,OAGd,OAAOnS,MAAIQ,OAAO4R,QAGpBF,MAAO9R,MACL,MAAMyO,IAAM9iB,KAAK4Y,MAAMvF,OACjBiT,IAAMxD,IAAI9O,WAAahU,KAAKgmB,QAElC,GAAI3R,MAAQiS,IAAK,CACf,MAAMC,IAAMvmB,KAAKgmB,QAAUlD,IAAIF,SAAS5iB,KAAKgmB,QAASlD,IAAI9O,YAAc8O,IAKxE,OAJA9iB,KAAK4Y,MAAMzF,QACXnT,KAAKgmB,QAAU,EACfhmB,KAAK6Y,UAAYyN,IACjBtmB,KAAK+lB,SAAWO,IACTC,IAMT,OAHAvmB,KAAK6Y,UAAYxE,KACjBrU,KAAK+lB,SAAW1R,KAETyO,IAAIF,SAAS5iB,KAAKgmB,QAAUhmB,KAAKgmB,SAAW3R,OAIvD,MAAMmS,eAAenI,WACnBxe,YAAa+K,KAAMoD,OAAQgH,QACzB2I,QAEA3d,KAAKgO,OAASA,OACdhO,KAAKgV,OAASA,OAEdhV,KAAKymB,QAAU7b,KAGjBwR,MAAO1C,IACoB,IAArB1Z,KAAKgO,OAAOqG,MACdrU,KAAK+E,KAAK,MAER/E,KAAKymB,QAAQC,UAAY1mB,MAC3BA,KAAKymB,QAAQE,UAEfjN,GAAG,MAGLoE,cACE9d,KAAKymB,QAAQ7c,QAAQiY,iBAAe7hB,OAGtC4mB,UACM5mB,KAAKymB,QAAQC,UAAY1mB,OAC3BA,KAAKymB,QAAQC,QAAU,KACvB1mB,KAAKymB,QAAQI,SAAWC,WAAS9mB,KAAKgO,OAAOqG,MAC7CrU,KAAKymB,QAAQE,WAIjB/L,SAAUlB,IACR1Z,KAAK4mB,UACLlN,GAAG,OAIP,MAAMqN,gBAAgBzf,WACpBzH,YAAa6d,MACXC,MAAMD,MAEDA,OAAMA,KAAO,IAElB1d,KAAKgnB,QAAU,IAAIlB,WACnB9lB,KAAKgmB,QAAU,EACfhmB,KAAKinB,QAAU,KACfjnB,KAAK0mB,QAAU,KACf1mB,KAAK6mB,SAAW,EAChB7mB,KAAKknB,aAAc,EACnBlnB,KAAKmnB,UAAYrd,OACjB9J,KAAKonB,SAAU,EACfpnB,KAAKqnB,WAAY,EACjBrnB,KAAKsnB,KAAO,KACZtnB,KAAKunB,WAAa,KAClBvnB,KAAKwnB,aAAe,KACpBxnB,KAAKynB,iBAAmB,KACxBznB,KAAK0nB,kBAAoBhK,KAAK2H,kBAAoB,QAClDrlB,KAAK2nB,sBAAwBjK,KAAK4H,mBAClCtlB,KAAK4nB,aAAe5nB,KAAK6nB,QAAQljB,KAAK3E,MAGxC6nB,QAAS3K,KAGP,GAFAld,KAAKonB,SAAU,EAEXlK,IAGF,OAFAld,KAAK4J,QAAQsT,UACbld,KAAK8nB,eAAe5K,KAItBld,KAAK2mB,UAGPoB,iBACE,GAAI/nB,KAAKonB,QAAS,OAAO,EAEzBpnB,KAAKgmB,QAAUhmB,KAAKgnB,QAAQjB,QAE5B,IACE/lB,KAAKinB,QAAUnZ,UAAQ4I,OAAO1W,KAAKgnB,QAAQ7T,MAAM,KAAMnT,KAAK0nB,kBAAmB1nB,KAAK2nB,qBACpF,MAAOzK,KAEP,OADAld,KAAK8nB,eAAe5K,MACb,EAGT,IAAKld,KAAKinB,QAAS,OAAO,EAE1B,OAAQjnB,KAAKinB,QAAQnnB,MACnB,IAAK,gBACL,IAAK,qBACL,IAAK,oBACL,IAAK,aAGH,OAFAE,KAAKknB,aAAc,EACnBlnB,KAAK6mB,SAAW7mB,KAAKinB,QAAQ5S,MACtB,EAMX,OAHArU,KAAKonB,SAAU,EACfpnB,KAAKgoB,oBAEqB,IAAtBhoB,KAAKinB,QAAQ5S,MAAoC,cAAtBrU,KAAKinB,QAAQnnB,MAC1CE,KAAKoJ,KAAK,QAASpJ,KAAKinB,QAASjnB,KAAKioB,gBAAiBjoB,KAAK4nB,eACrD,IAGT5nB,KAAK0mB,QAAU1mB,KAAKioB,gBACpBjoB,KAAK6mB,SAAW7mB,KAAKinB,QAAQ5S,KAE7BrU,KAAKoJ,KAAK,QAASpJ,KAAKinB,QAASjnB,KAAK0mB,QAAS1mB,KAAK4nB,eAC7C,GAGTI,oBACMhoB,KAAKwnB,eACPxnB,KAAKinB,QAAQ1mB,KAAOP,KAAKwnB,aACzBxnB,KAAKwnB,aAAe,MAGlBxnB,KAAKynB,mBACPznB,KAAKinB,QAAQjD,SAAWhkB,KAAKynB,iBAC7BznB,KAAKynB,iBAAmB,MAGtBznB,KAAKsnB,OACHtnB,KAAKsnB,KAAK5Z,OAAM1N,KAAKinB,QAAQ1mB,KAAOP,KAAKsnB,KAAK5Z,MAC9C1N,KAAKsnB,KAAKY,WAAUloB,KAAKinB,QAAQjD,SAAWhkB,KAAKsnB,KAAKY,UACtDloB,KAAKsnB,KAAKjT,OAAMrU,KAAKinB,QAAQ5S,KAAOkP,SAASvjB,KAAKsnB,KAAKjT,KAAM,KACjErU,KAAKinB,QAAQhD,IAAMjkB,KAAKsnB,KACxBtnB,KAAKsnB,KAAO,MAIhBa,kBAAmBrF,KACjB,OAAQ9iB,KAAKinB,QAAQnnB,MACnB,IAAK,gBACHE,KAAKwnB,aAAe1Z,UAAQgW,eAAehB,IAAK9iB,KAAK0nB,mBACrD,MACF,IAAK,qBACH1nB,KAAKynB,iBAAmB3Z,UAAQgW,eAAehB,IAAK9iB,KAAK0nB,mBACzD,MACF,IAAK,oBACH1nB,KAAKunB,WAAazZ,UAAQoW,UAAUpB,KACpC,MACF,IAAK,aACH9iB,KAAKsnB,KAA2B,OAApBtnB,KAAKunB,WACbzZ,UAAQoW,UAAUpB,KAClBliB,OAAOC,OAAO,GAAIb,KAAKunB,WAAYzZ,UAAQoW,UAAUpB,OAK/DsF,qBACEpoB,KAAKknB,aAAc,EACnBlnB,KAAK6mB,SAAWC,WAAS9mB,KAAKinB,QAAQ5S,MAEtC,MAAMyO,IAAM9iB,KAAKgnB,QAAQ7T,MAAMnT,KAAKinB,QAAQ5S,MAE5C,IACErU,KAAKmoB,kBAAkBrF,KACvB,MAAO5F,KAEP,OADAld,KAAK8nB,eAAe5K,MACb,EAGT,OAAO,EAGTmL,iBACE,MAAMvF,IAAM9iB,KAAKgnB,QAAQf,WAAWjmB,KAAK6mB,UACzC,GAAY,OAAR/D,IAAc,OAAO,EAEzB9iB,KAAK6mB,UAAY/D,IAAI9O,WACrB,MAAMsU,QAAUtoB,KAAK0mB,QAAQ3hB,KAAK+d,KAElC,OAAsB,IAAlB9iB,KAAK6mB,UACP7mB,KAAK0mB,QAAQ3hB,KAAK,MACdujB,SAAStoB,KAAK0mB,QAAQE,UACnB0B,UAA4B,IAAjBtoB,KAAKonB,SAGlBkB,QAGTL,gBACE,OAAO,IAAIzB,OAAOxmB,KAAMA,KAAKinB,QAASjnB,KAAKgmB,SAG7CW,UACE,KAAO3mB,KAAKgnB,QAAQnO,SAAW,IAAM7Y,KAAKme,YACxC,GAAIne,KAAK6mB,SAAW,EAApB,CACE,GAAqB,OAAjB7mB,KAAK0mB,QAAkB,CACzB,IAA8B,IAA1B1mB,KAAKqoB,iBAA4B,OACrC,SAGF,IAAyB,IAArBroB,KAAKknB,YAAsB,CAC7B,GAAIlnB,KAAK6mB,SAAW7mB,KAAKgnB,QAAQnO,SAAU,MAC3C,IAAkC,IAA9B7Y,KAAKooB,qBAAgC,OAAO,EAChD,SAGF,MAAMG,OAASvoB,KAAKgnB,QAAQf,WAAWjmB,KAAK6mB,UAC7B,OAAX0B,SAAiBvoB,KAAK6mB,UAAY0B,OAAOvU,gBAb/C,CAiBA,GAAIhU,KAAKgnB,QAAQnO,SAAW,IAAK,MACjC,GAAqB,OAAjB7Y,KAAK0mB,UAA8C,IAA1B1mB,KAAK+nB,iBAA4B,OAGhE/nB,KAAK8nB,eAAe,MAGtBA,eAAgB5K,KACd,MAAMxD,GAAK1Z,KAAKmnB,UAChBnnB,KAAKmnB,UAAYrd,OACjB4P,GAAGwD,KAGLjD,OAAQpL,KAAM6K,IACZ1Z,KAAKmnB,UAAYzN,GACjB1Z,KAAKgnB,QAAQjiB,KAAK8J,MAClB7O,KAAK2mB,UAGPtM,OAAQX,IACN1Z,KAAKqnB,UAA8B,IAAlBrnB,KAAK6mB,UAA4C,IAA1B7mB,KAAKgnB,QAAQnO,SACrDa,GAAG1Z,KAAKqnB,UAAY,KAAO,IAAIzgB,MAAM,2BAGvCkX,cACE9d,KAAK8nB,eAAe,MAGtBlN,SAAUlB,IACJ1Z,KAAK0mB,SAAS1mB,KAAK0mB,QAAQ9c,QAAQiY,iBAAe7hB,OACtD0Z,GAAG,MAGL,CAACpB,OAAOD,iBACN,IAAI1Q,MAAQ,KAER2X,eAAiB,KACjBC,cAAgB,KAEhBiJ,YAAc,KACdC,cAAgB,KAEpB,MAAMC,QAAU1oB,KAMhB,OAJAA,KAAK2K,GAAG,SAgDR,SAASge,QAAS3a,OAAQwK,OAAQvM;AAChCwc,cAAgBxc,SAChBuM,OAAO7N,GAAG,QAASb,QAEfwV,gBACFA,eAAe,CAAEvT,MAAOyM,OAAQoD,MAAM,IACtC0D,eAAiBC,cAAgB,MAEjCiJ,YAAchQ,UAvDlBxY,KAAK2K,GAAG,SAAUuS,MAAUvV,MAAQuV,OACpCld,KAAK2K,GAAG,SA0DR,SAASkR,UAEP,GADA+M,gBAAgBjhB,QACX2X,eAAgB,OACjB3X,MAAO4X,cAAc5X,OACpB2X,eAAe,CAAEvT,WAAOkC,EAAW2N,MAAM,IAC9C0D,eAAiBC,cAAgB,QA7D5B,CACL,CAACjH,OAAOD,iBACN,OAAOrY,MAETgT,OACE,OAAO,IAAI0M,QAAQmJ,SAErB1J,SACE,OAAOvV,QAAQ,OAEjBgW,MAAO1C,KACL,OAAOtT,QAAQsT,OAInB,SAAS0L,gBAAiB1L,KACxB,IAAKuL,cAAe,OACpB,MAAM/O,GAAK+O,cACXA,cAAgB,KAChB/O,GAAGwD,KAGL,SAAS2L,OAAQ3W,QAASyN,QACxB,OAAIhY,MACKgY,OAAOhY,OAGZ6gB,aACFtW,QAAQ,CAAEnG,MAAOyc,YAAa5M,MAAM,SACpC4M,YAAc,QAIhBlJ,eAAiBpN,QACjBqN,cAAgBI,OAEhBiJ,gBAAgB,WAEZF,QAAQrB,WAAa/H,iBACvBA,eAAe,CAAEvT,WAAOkC,EAAW2N,MAAM,IACzC0D,eAAiBC,cAAgB,QAwBrC,SAAS3V,QAASsT,KAGhB,OAFAwL,QAAQ9e,QAAQsT,KAChB0L,gBAAgB1L,KACT,IAAIwC,SAAQ,CAACxN,QAASyN,UAC3B,GAAI+I,QAAQxK,UAAW,OAAOhM,QAAQ,CAAEnG,WAAOkC,EAAW2N,MAAM,IAChE8M,QAAQ9Y,KAAK,SAAS,WAChBsN,IAAKyC,OAAOzC,KACXhL,QAAQ,CAAEnG,WAAOkC,EAAW2N,MAAM,YAWjD,SAAS9R,UAET,SAASgd,WAAUzS,MAEjB,OADAA,MAAQ,MACO,IAAMA,kCCpZvB,MAAMyU,YAAY;AAChBC,OAAQ,MACRC,QAAS,MACTC,QAAS,KACTC,QAAS,MACTC,QAAS,KACTC,QAAS,OAGX,IACEC,oBAAiB3hB,QAAQ,MAAMohB,WAAaA,YAC5C,MACAO,oBAAiBP,YCZnB,MAAMzK,SAAEA,SAAQ/W,SAAEA,SAAQua,eAAEA,gBAAmB7a,QACzCiN,IAAM9M,MAEN2hB,UAAYzhB,oBACZyG,QAAUvG,UAKV+hB,WAAarV,IAAIG,MAAM,MAE7B,MAAMmV,aAAajiB,SACjBzH,YAAa2pB,KAAMxb,OAAQ/B,UACzB0R,MAAM,CAAEjF,YAAAA,YAAa6F,WAAW,IAEhCve,KAAKypB,QAAU,EACfzpB,KAAKgO,OAASA,OAEdhO,KAAKmnB,UAAYlb,SACjBjM,KAAK0pB,UAAY,KACjB1pB,KAAK2pB,YAA8B,YAAhB3b,OAAOlO,OAAuBkO,OAAOgW,SACxDhkB,KAAK4pB,QAA0B,SAAhB5b,OAAOlO,MAAmC,oBAAhBkO,OAAOlO,KAChDE,KAAKqnB,WAAY,EACjBrnB,KAAK6pB,MAAQL,KACbxpB,KAAK8pB,cAAgB,KAEM,OAAvB9pB,KAAK6pB,MAAMnD,QAAkB1mB,KAAK6pB,MAAMnD,QAAU1mB,KACjDA,KAAK6pB,MAAME,SAAShlB,KAAK/E,MAGhCya,MAAOf,IACL1Z,KAAK8pB,cAAgBpQ,GACjB1Z,KAAK6pB,MAAMnD,UAAY1mB,MAAMA,KAAKgqB,gBAGxCC,cAAe/M,KACb,GAAuB,OAAnBld,KAAKmnB,UAAoB,OAE7B,MAAMlb,SAAWjM,KAAKmnB,UACtBnnB,KAAKmnB,UAAY,KAEjBlb,SAASiR,KAGX8M,gBAC6B,OAAvBhqB,KAAK6pB,MAAMnD,UAAkB1mB,KAAK6pB,MAAMnD,QAAU1mB,MAEtD,MAAM0Z,GAAK1Z,KAAK8pB,cAEhB,GADA9pB,KAAK8pB,cAAgB,KACV,OAAPpQ,GAAJ,CAEA,GAAI1Z,KAAK6pB,MAAM1L,WAAY,OAAOzE,GAAG,IAAI9S,MAAM,0BAC/C,GAAI5G,KAAK6pB,MAAMK,WAAY,OAAOxQ,GAAG,IAAI9S,MAAM,qCAE/C5G,KAAK6pB,MAAMnD,QAAU1mB,KAEhBA,KAAK2pB,aACR3pB,KAAK6pB,MAAMM,QAAQnqB,KAAKgO,QAGtBhO,KAAK4pB,UACP5pB,KAAKoqB,UACLpqB,KAAKiqB,cAAc,OAGrBvQ,GAAG,OAGLO,OAAQpL,KAAM6K,IACZ,OAAI1Z,KAAK2pB,aACP3pB,KAAK0pB,UAAY1pB,KAAK0pB,UAAYzV,IAAIQ,OAAO,CAACzU,KAAK0pB,UAAW7a,OAASA,KAChE6K,GAAG,OAGR1Z,KAAK4pB,QACH/a,KAAKmF,WAAa,EACb0F,GAAG,IAAI9S,MAAM,mCAEf8S,MAGT1Z,KAAKypB,SAAW5a,KAAKmF,WACjBhU,KAAK6pB,MAAM9kB,KAAK8J,MAAc6K,UAClC1Z,KAAK6pB,MAAMQ,OAAS3Q,KAGtB0Q,UACMpqB,KAAKqnB,YACTrnB,KAAKqnB,WAAY,EAEbrnB,KAAK2pB,cACP3pB,KAAKgO,OAAOgW,SAAWhkB,KAAK0pB,UAAYzV,IAAI9T,SAASH,KAAK0pB,UAAW,SAAW,GAChF1pB,KAAK6pB,MAAMM,QAAQnqB,KAAKgO,SAG1B8Y,SAAS9mB,KAAK6pB,MAAO7pB,KAAKgO,OAAOqG,MAEjCrU,KAAK6pB,MAAMS,MAAMtqB,OAGnBqa,OAAQX,IACN,GAAI1Z,KAAKypB,UAAYzpB,KAAKgO,OAAOqG;AAC/B,OAAOqF,GAAG,IAAI9S,MAAM,kBAGtB5G,KAAKoqB,UACL1Q,GAAG,MAGL6Q,YACE,OAAO1I,eAAe7hB,OAAS,IAAI4G,MAAM,uBAG3CkX,cACE9d,KAAK6pB,MAAMjgB,QAAQ5J,KAAKuqB,aAG1B3P,SAAUlB,IACR1Z,KAAK6pB,MAAMS,MAAMtqB,MAEjBA,KAAKiqB,cAAcjqB,KAAKqnB,UAAY,KAAOrnB,KAAKuqB,aAEhD7Q,MAIJ,MAAM8Q,aAAanM,SACjBxe,YAAa6d,MACXC,MAAMD,MACN1d,KAAKqqB,OAASvgB,OACd9J,KAAKkqB,YAAa,EAClBlqB,KAAKyqB,aAAc,EACnBzqB,KAAK+pB,SAAW,GAChB/pB,KAAK0mB,QAAU,KAGjBgE,MAAO1c,OAAQ6C,OAAQ5E,UACrB,GAAIjM,KAAKkqB,YAAclqB,KAAKme,WAAY,MAAM,IAAIvX,MAAM,kCAElC,mBAAXiK,SACT5E,SAAW4E,OACXA,OAAS,MAGN5E,WAAUA,SAAWnC,QAErBkE,OAAOqG,MAAwB,YAAhBrG,OAAOlO,OAAoBkO,OAAOqG,KAAO,GACxDrG,OAAOlO,OAAMkO,OAAOlO,KAsH7B,SAAS6qB,WAAYpG,MACnB,OAAQA,KAAOuE,UAAUC,QACvB,KAAKD,UAAUI,QAAS,MAAO,eAC/B,KAAKJ,UAAUG,QAAS,MAAO,mBAC/B,KAAKH,UAAUE,QAAS,MAAO,YAC/B,KAAKF,UAAUK,QAAS,MAAO,OAC/B,KAAKL,UAAUM,QAAS,MAAO,UAGjC,MAAO,OA/H2BuB,CAAW3c,OAAOuW,OAC7CvW,OAAOuW,OAAMvW,OAAOuW,KAAuB,cAAhBvW,OAAOlO,KA9I7B,IACA,KA8ILkO,OAAOwW,MAAKxW,OAAOwW,IAAM,GACzBxW,OAAOyW,MAAKzW,OAAOyW,IAAM,GACzBzW,OAAO6W,QAAO7W,OAAO6W,MAAQ,IAAIhhB,MAEhB,iBAAXgN,SAAqBA,OAASoD,IAAI5Q,KAAKwN,SAElD,MAAM+Z,KAAO,IAAIrB,KAAKvpB,KAAMgO,OAAQ/B,UAEpC,OAAIgI,IAAIlF,SAAS8B,SACf7C,OAAOqG,KAAOxD,OAAOmD,WACrB4W,KAAKhc,MAAMiC,QACX+Z,KAAK9d,MACE8d,OAGLA,KAAKhB,QACAgB,MAMXC,WACM7qB,KAAK0mB,SAAW1mB,KAAK+pB,SAAS1jB,OAAS,EACzCrG,KAAKyqB,aAAc,EAIjBzqB,KAAKkqB,aACTlqB,KAAKkqB,YAAa,EAElBlqB,KAAK+E,KAAKukB,YACVtpB,KAAK+E,KAAK,OAGZulB,MAAO9R,QACDA,SAAWxY,KAAK0mB,UAEpB1mB,KAAK0mB,QAAU,KAEX1mB,KAAKyqB,aAAazqB,KAAK6qB,WACvB7qB,KAAK+pB,SAAS1jB,QAAQrG,KAAK+pB,SAAS5W,QAAQ6W,iBAGlDG,QAASnc,QACP,IAAKA,OAAOiW,IAAK,CACf,MAAMnB,IAAMhV,QAAQsW,OAAOpW,QAC3B,GAAI8U,IAEF,YADA9iB,KAAK+E,KAAK+d,KAId9iB,KAAK8qB,WAAW9c,QAGlB8c,WAAY9c,QACV,MAAM+c,UAAYjd,QAAQiW,UAAU,CAClCxjB,KAAMyN,OAAOzN,KACbyjB,SAAUhW,OAAOgW,SACjBC,IAAKjW,OAAOiW,MAGR+G,UAAY,CAChBzqB,KAAM,YACNgkB,KAAMvW,OAAOuW,KACbC,IAAKxW,OAAOwW,IACZC,IAAKzW,OAAOyW,IACZpQ,KAAM0W,UAAU/W,WAChB6Q,MAAO7W,OAAO6W,MACd/kB,KAAM,aACNkkB,SAAUhW,OAAOgW,UAAY,YAC7BiB,MAAOjX,OAAOiX,MACdC,MAAOlX,OAAOkX,MACdC,SAAUnX,OAAOmX,SACjBC,SAAUpX,OAAOoX,UAGnBplB,KAAK+E,KAAK+I,QAAQsW,OAAO4G,YACzBhrB,KAAK+E,KAAKgmB,WACVjE,SAAS9mB,KAAM+qB,UAAU/W,YAEzBgX,UAAU3W,KAAOrG,OAAOqG,KACxB2W,UAAUlrB,KAAOkO,OAAOlO,KACxBE,KAAK+E,KAAK+I,QAAQsW,OAAO4G,YAG3BC,WACE,MAAM/O,MAAQlc,KAAKqqB,OACnBrqB,KAAKqqB,OAASvgB,OACdoS,QAGF4B,cACE,MAAMZ,IAAM2E,eAAe7hB,MAI3B,IAFIA,KAAK0mB,SAAS1mB,KAAK0mB,QAAQ9c,QAAQsT,KAEhCld,KAAK+pB,SAAS1jB,QAAQ,CAC3B,MAAMmS,OAASxY,KAAK+pB,SAAS5W,QAC7BqF,OAAO5O,QAAQsT,KACf1E,OAAOwR,gBAGThqB,KAAKirB,WAGP7O,MAAO1C,IACL1Z,KAAKirB,WACLvR,MAoBJ,SAAS5P,UAET,SAASgd,SAAUlc,KAAMyJ,OACvBA,MAAQ,MACEzJ,KAAK7F,KAAKukB,WAAW1G,SAAS,EAAG,IAAMvO,OAGnD,SAASqE,YAAaoK,KACpB,OAAO7O,IAAIlF,SAAS+T,KAAOA,IAAM7O,IAAI5Q,KAAKyf,uBF+G3B,SAAS4F,QAAShL,MACjC,OAAO,IAAIqJ,QAAQrJ,sBExIJ,SAAS8L,KAAM9L,MAC9B,OAAO,IAAI8M,KAAK9M,+BCjQlBwN,SACA,SAASC,SAAQjT,GAAIwB,IACnB,GAAIxB,IAAMwB,GAAI,OAAOyR,SAAOjT,GAAPiT,CAAWzR,IAEhC,GAAkB,mBAAPxB,GACT,MAAM,IAAI3O,UAAU,yBAMtB,OAJA3I,OAAO4K,KAAK0M,IAAIzT,SAAQ,SAAU2mB,GAChCC,QAAQD,GAAKlT,GAAGkT,MAGXC,QAEP,SAASA,UAEP,IADA,IAAIloB,KAAO,IAAID,MAAM2E,UAAUxB,QACtBqK,EAAI,EAAGA,EAAIvN,KAAKkD,OAAQqK,IAC/BvN,KAAKuN,GAAK7I,UAAU6I,GAEtB,IAAI4a,IAAMpT,GAAGtQ,MAAM5H,KAAMmD,MACrBuW,GAAKvW,KAAKA,KAAKkD,OAAO,GAM1B,MALmB,mBAARilB,KAAsBA,MAAQ5R,IACvC9Y,OAAO4K,KAAKkO,IAAIjV,SAAQ,SAAU2mB,GAChCE,IAAIF,GAAK1R,GAAG0R,MAGTE;;;;;mDC9BX;IAAIH,OAASnkB,SAoBb,SAAS4I,OAAMsI,IACb,IAAIqT,EAAI,WACN,OAAIA,EAAEC,OAAeD,EAAExf,OACvBwf,EAAEC,QAAS,EACJD,EAAExf,MAAQmM,GAAGtQ,MAAM5H,KAAM6H,aAGlC,OADA0jB,EAAEC,QAAS,EACJD,EAGT,SAASE,WAAYvT,IACnB,IAAIqT,EAAI,WACN,GAAIA,EAAEC,OACJ,MAAM,IAAI5kB,MAAM2kB,EAAEG,WAEpB,OADAH,EAAEC,QAAS,EACJD,EAAExf,MAAQmM,GAAGtQ,MAAM5H,KAAM6H,YAE9BtH,KAAO2X,GAAG3X,MAAQ,+BAGtB,OAFAgrB,EAAEG,UAAYnrB,KAAO,sCACrBgrB,EAAEC,QAAS,EACJD,EAvCTI,eAAiBR,OAAOvb,8BACAub,OAAOM,YAE/B7b,OAAKgc,MAAQhc,QAAK,WAChBhP,OAAOqP,eAAe4b,SAAShiB,UAAW,OAAQ,CAChDkC,MAAO,WACL,OAAO6D,OAAK5P,OAEd0M,cAAc,IAGhB9L,OAAOqP,eAAe4b,SAAShiB,UAAW,aAAc,CACtDkC,MAAO,WACL,OAAO0f,WAAWzrB,OAEpB0M,cAAc,OChBlB,ICEIof,KDFAlc,OAAO5I,eAEP8C,OAAO,aAEPiiB,IAAM5T,eAAO6T,KAAO/T,eAAiBjQ,QAAQoQ,SAASzT,KAAKqD,SAU3DikB,MAAM,SAASzT,OAAQkF,KAAMzR,UAChC,GAAoB,mBAATyR,KAAqB,OAAOuO,MAAIzT,OAAQ,KAAMkF,MACpDA,OAAMA,KAAO,IAElBzR,SAAW2D,OAAK3D,UAAYnC,QAE5B,IAAIuT,GAAK7E,OAAOoB,eACZuD,GAAK3E,OAAO4E,eACZa,SAAWP,KAAKO,WAA+B,IAAlBP,KAAKO,UAAsBzF,OAAOyF,SAC/DrR,SAAW8Q,KAAK9Q,WAA+B,IAAlB8Q,KAAK9Q,UAAsB4L,OAAO5L,SAC/Dsf,WAAY,EAEZC,eAAiB,WACf3T,OAAO5L,UAAUwf,YAGnBA,SAAW,WACdxf,UAAW,EACNqR,UAAUhS,SAAS/B,KAAKsO,SAG1B6T,MAAQ,WACXpO,UAAW,EACNrR,UAAUX,SAAS/B,KAAKsO,SAG1B8T,OAAS,SAASC,UACrBtgB,SAAS/B,KAAKsO,OAAQ+T,SAAW,IAAI3lB,MAAM,2BAA6B2lB,UAAY,OAGjF5Q,QAAU,SAASuB,KACtBjR,SAAS/B,KAAKsO,OAAQ0E,MAGnBrB,QAAU,WACbkQ,IAAIS,kBAGDA,gBAAkB,WACrB,IAAIN,UACJ,QAAIjO,UAAcd,IAAOA,GAAG/D,QAAU+D,GAAGe,cACrCtR,UAAcyQ,IAAOA,GAAGjE,QAAUiE,GAAGa,gBAAzC,EAD6DjS,SAAS/B,KAAKsO,OAAQ,IAAI5R,MAAM,qBAI1F6lB,UAAY,WACfjU,OAAOhH,IAAI7G,GAAG,SAAUyhB,WAoBzB,OAzEe,SAAS5T,QACxB,OAAOA,OAAOvJ,WAAqC,mBAAjBuJ,OAAO9J,MAuDrCge,CAAUlU,QAKH5L,WAAayQ;AACvB7E,OAAO7N,GAAG,MAAOwhB,gBACjB3T,OAAO7N,GAAG,QAASwhB,kBANnB3T,OAAO7N,GAAG,WAAYyhB,UACtB5T,OAAO7N,GAAG,QAASkR,SACfrD,OAAOhH,IAAKib,YACXjU,OAAO7N,GAAG,UAAW8hB,YAxDP,SAASjU,QAC7B,OAAOA,OAAOmU,OAASzpB,MAAMsB,QAAQgU,OAAOmU,QAAkC,IAAxBnU,OAAOmU,MAAMtmB,OA6D/DumB,CAAepU,SAASA,OAAO7N,GAAG,OAAQ2hB,QAE9C9T,OAAO7N,GAAG,MAAO0hB,OACjB7T,OAAO7N,GAAG,SAAUyhB,WACD,IAAf1O,KAAK/V,OAAiB6Q,OAAO7N,GAAG,QAASgR,SAC7CnD,OAAO7N,GAAG,QAASkR,SAEZ,WACNqQ,WAAY,EACZ1T,OAAO/J,eAAe,WAAY2d,UAClC5T,OAAO/J,eAAe,QAASoN,SAC/BrD,OAAO/J,eAAe,UAAWge,WAC7BjU,OAAOhH,KAAKgH,OAAOhH,IAAI/C,eAAe,SAAU2d,UACpD5T,OAAO/J,eAAe,MAAO0d,gBAC7B3T,OAAO/J,eAAe,QAAS0d,gBAC/B3T,OAAO/J,eAAe,SAAU2d,UAChC5T,OAAO/J,eAAe,OAAQ6d,QAC9B9T,OAAO/J,eAAe,MAAO4d,OAC7B7T,OAAO/J,eAAe,QAASkN,SAC/BnD,OAAO/J,eAAe,QAASoN,WAIjCgR,YAAiBZ,MC/Fbrc,KAAO5I,eACPilB,IAAM9kB,YAGV,IACE2kB,KAAKpkB,QAAQ;CACb,MAAOolB,IAET,IAAIhjB,OAAO,aACPijB,QAA6B,oBAAZ/kB,SAAkC,SAASgF,KAAKhF,QAAQglB,SAEzEC,KAAO,SAAU/U,IACnB,MAAqB,mBAAPA,IAaZgV,UAAY,SAAU1U,OAAQ2U,QAASC,QAASnhB,UAClDA,SAAW2D,KAAK3D,UAEhB,IAAIohB,QAAS,EACb7U,OAAO7N,GAAG,SAAS,WACjB0iB,QAAS,KAGXpB,IAAIzT,OAAQ,CAACyF,SAAUkP,QAASvgB,SAAUwgB,UAAU,SAAUlQ,KAC5D,GAAIA,IAAK,OAAOjR,SAASiR,KACzBmQ,QAAS,EACTphB,cAGF,IAAIiS,WAAY,EAChB,OAAO,SAAUhB,KACf,IAAImQ,SACAnP,UAGJ,OAFAA,WAAY,EA5BL,SAAU1F,QACnB,QAAKuU,WACAjB,OACGtT,kBAAmBsT,KAAGwB,YAAcxjB,SAAS0O,kBAAmBsT,KAAGyB,aAAezjB,UAAUmjB,KAAKzU,OAAOgV;CA2B1GC,CAAKjV,QAAgBA,OAAOgV,MAAM1jB;AAxB1B,SAAU0O,QACxB,OAAOA,OAAOvJ,WAAage,KAAKzU,OAAO9J,OAwBjCge,CAAUlU,QAAgBA,OAAO9J;AAEjCue,KAAKzU,OAAO5O,SAAiB4O,OAAO5O,eAExCqC,SAASiR,KAAO,IAAItW,MAAM,2BAI1BsD,KAAO,SAAUgO,IACnBA,MAGEsD,KAAO,SAAUnY,KAAM0Z,IACzB,OAAO1Z,KAAKmY,KAAKuB,KC3DnB,MAAM2Q,IAAM1mB,UACN2mB,KD6DK,WACT,IAMIhmB,MANAmZ,QAAU5d,MAAM2G,UAAU0D,MAAMrD,KAAKrC,WACrCoE,SAAWghB,KAAKnM,QAAQA,QAAQza,OAAS,IAAMyD,SAASgX,QAAQE,OAASlX,OAG7E,GADI5G,MAAMsB,QAAQsc,QAAQ,MAAKA,QAAUA,QAAQ,IAC7CA,QAAQza,OAAS,EAAG,MAAM,IAAIO,MAAM,yCAGxC,IAAIgnB,SAAW9M,QAAQ7f,KAAI,SAAUuX,OAAQ9H,GAC3C,IAAIyc,QAAUzc,EAAIoQ,QAAQza,OAAS,EAEnC,OAAO6mB,UAAU1U,OAAQ2U,QADXzc,EAAI,GACyB,SAAUwM,KAC9CvV,QAAOA,MAAQuV,KAChBA,KAAK0Q,SAASnpB,QAAQyF,MACtBijB,UACJS,SAASnpB,QAAQyF,MACjB+B,SAAStE,cAIb,OAAOmZ,QAAQ3f,OAAOqa,OChFlBsQ,GAAKzkB,8BACLqG,KAAOnG,8BAEPsmB,MAAoE,WAA3D1V,eAAO6T,KAAO7T,eAAO6T,KAAK8B,SAAW9lB,QAAQ8lB,UAiH5D,YAAkB,SAASpF,QAASqF,IAAKrQ,MAClCqQ,MAAKA,IAAM,KACXrQ,OAAMA,KAAO,IAElBqQ,IAAMrgB,KAAKwE,QAAQ6b,KAEnB,MAAMC,IAAMtQ,KAAKoO,IAAMA,GACjBvD,OAAS7K,KAAK6K,QAAU7K,KAAKrY,QAAUyE,KACvCmkB,UAAYvQ,KAAKuQ,WAAavP,KAC9BwP,KAAqB,IAAfxQ,KAAKyQ,QAAoBN,OAA6B,IAjBpE,SAASO,gBACP,OAASjW,eAAO6T,MAAQhkB,QAAQqmB,OAAUrmB,QAAQqmB,UAAY,EAgBhBD,GACxC1F,QAAUhL,KAAKgL,SAAWgF,IAAIhF,UAC9B4F,MAAQ,GACRC,IAAM,IAAI1qB,KACV2qB,MAA8B,iBAAf9Q,KAAK8Q,OAAsB9Q,KAAK8Q,OAjBvD,SAASC,eACP,OAAStW,eAAO6T,MAAQhkB,QAAQwmB,MAASxmB,QAAQwmB,QAAU,EAgBGC,GACxDztB,QAAyB,IAAhB0c,KAAK1c,OACd0tB,kBAA6C,IAA1BhR,KAAKiR,iBAE9B,IAAI1tB,IAAMyc,KAAKzc,KAAO6I,KAClB8kB,MAA8B,iBAAflR,KAAKkR,MAAqBlR,KAAKkR,MAAQ,EACtDC,MAA8B,iBAAfnR,KAAKmR,MAAqBnR,KAAKmR,MAAQ,EAiB1D,OAfInR,KAAKoR,QAAO7tB,IA0OlB,SAAS6tB,MAAO7tB,IAAK8tB,OACnB,OAAO,SAAU/gB,QACfA,OAAOzN,KAAOyN,OAAOzN,KAAKyuB,MAAM,KAAKzhB,MAAMwhB,OAAOE,KAAK,KAEvD,MAAMjL,SAAWhW,OAAOgW,SAKxB,OAJIA,WAA6B,SAAhBhW,OAAOlO,MAAmB4N,KAAKwhB,WAAWlL,aACzDhW,OAAOgW,SAAWA,SAASgL,MAAM,KAAKzhB,MAAMwhB,OAAOE,KAAK,MAGnDhuB,IAAI+M;;;GAnPS8gB,CAAM7tB,IAAKyc,KAAKoR,QAElCpR,KAAKO,WACP2Q,OAASrL,SAAS,IAAK,GACvBsL,OAAStL,SAAS,IAAK,IAErB7F,KAAK9Q,WACPgiB,OAASrL,SAAS,IAAK,GACvBsL,OAAStL,SAAS,IAAK,IAGzBmF,QAAQ/d,GAAG,SAMX,SAASge,QAAS3a,OAAQwK,OAAQxF,OAChChF,OAAS/M,IAAI+M,SAAWA,QACjBzN,KAoLX,SAAS4uB,UAAW5uB,MAClB,OAAOstB,MAAQttB,KAAK6uB,QAAQ,MAAO,KAAKA,QAAQ,WAAY,KAAO7uB,KArLnD4uB,CAAUnhB,OAAOzN,MAE/B,MAAMA,KAAOmN,KAAKuhB,KAAKlB,IAAKrgB,KAAKuhB,KAAK,IAAKjhB,OAAOzN,OAElD,GAAIgoB,OAAOhoB,KAAMyN,QAEf,OADAwK,OAAOqG,SACA7L,OAGT,MAAMqc,IAAM3hB,KAAKuhB,KAAK1uB,KAAM,OAASmN,KAAKuhB,KAAKlB,IAAK,KAAOA,IAAMrgB,KAAK4hB,QAAQ/uB,MA0C9E,SAASgvB,KAAMrS,KACb,GAAIA,IAAK,OAAOlK,KAAKkK,MAgEzB,SAASsS,OAAQjvB,KAAMyN,OAAQ0L,IAC7B,IAAoB,IAAhBgE,KAAK8R,OAAkB,OAAO9V,KAElC,GAAoB,cAAhB1L,OAAOlO,KAAsB,OAAOkuB,IAAIwB,OAAOjvB,KAAMguB,IAAKvgB,OAAO6W,MAAOnL,IAC5E,GAAoB,YAAhB1L,OAAOlO,KAAoB,OAAO2vB,aAAalvB,KAAMmZ,sCAEzDsU;IAAIwB,OAAOjvB,KAAMguB,IAAKvgB,OAAO6W,OAAO,SAAU3H,KAC5C,GAAIA,IAAK,OAAOxD,GAAGwD,KACnBuS,aAAalvB,KAAMmZ,OAvEnB8V,CAAOjvB,KAAMyN,QAAQ,SAAUkP,KAC7B,OAAIA,IAAYlK,KAAKkK,KACjB2Q,MAAc7a,YAClB0c,OAAOnvB,KAAMyN,OAAQgF,SAkCzB,SAAS2c,MAAO7S,KACd,OAAOA,IAAIxP,WAAWygB,KAGxB,SAAS6B,SACP,MAAMvS,GAAK2Q,IAAI6B,kBAAkBtvB,MAC3B4c,GAAK8Q,UAAUzV,OAAQxK,QAE7BqP,GAAG1S,GAAG,SAAS,SAAUuS;AACvBC,GAAGvT,QAAQsT,QAGbyQ,KAAKxQ,GAAIE,IAAI,SAAUH,KACrB,GAAIA,IAAK,OAAOlK,KAAKkK,KACrBG,GAAG1S,GAAG,QAAS4kB,SA7FnBO,SAAS9B,IAAKqB,IAAK3hB,KAAKuhB,KAAKlB,IAAK,MAAM,SAAU7Q,IAAK6S,OACrD,OAAI7S,IAAYlK,KAAKkK,KAChB6S,MAEe,cAAhB/hB,OAAOlO,MACTwuB,MAAMvpB,KAAK,CAACxE,KAAMyN,OAAO6W,QAClBmL,SAASzvB,KAAM,CACpBurB,GAAIkC,IACJE,IAAAA,IACA1J,IAAKxW,OAAOwW,IACZC,IAAKzW,OAAOyW,IACZF,KAAMvW,OAAOuW,MACZgL,YAGLS,SAASX,IAAK,CACZvD,GAAIkC,IACJE,IAAAA,IACA1J,IAAKxW,OAAOwW,IACZC,IAAKzW,OAAOyW;;;;AAIZF,KAAM,MACL,SAAUrH,KACX,GAAIA,IAAK,OAAOlK,KAAKkK,KAErB,OAAQlP,OAAOlO,MACb,IAAK,OAAQ,OAAO8vB,SACpB,IAAK,OAAQ,OA8BnB,SAASK,SACP,GAAIpC,MAAO,OAAO7a,2DAClBgb;IAAIkC,OAAO3vB,MAAM,WACf,MAAM4vB,KAAOziB,KAAKuhB,KAAKlB,IAAKrgB,KAAKuhB,KAAK,IAAKjhB,OAAOgW,WAElD8H,GAAGsE,SAASD,MAAM,SAAUjT,IAAKJ,KAC/B,GAAII,MAAQyS,MAAM7S,KAAM,OAAO9J,KAAK,IAAIpM,MAAMrG,KAAO,6BAErDytB,IAAImC,KAAKrT,IAAKvc,MAAM,SAAU2c,KAC5B,GAAIA,KAAoB,UAAbA,IAAIxU,MAAoBgV,KAAK2S,wBAEtC,OADA7X,OAASwV,IAAIsC,iBAAiBxT,KACvB8S,SAGTL,KAAKrS,cA5Ca+S,GACpB,IAAK,UAAW,OAmBtB,SAASM,YACP,GAAI1C,MAAO,OAAO7a,8DAClBgb;IAAIkC,OAAO3vB,MAAM,WAEf,IAAKovB,MADOjiB,KAAKwE,QAAQxE,KAAK4hB,QAAQ/uB,MAAOyN,OAAOgW,YACjC0K,iBAAkB,OAAO1b,KAAK,IAAIpM,MAAMrG,KAAO,4BAElEytB,IAAIwC,QAAQxiB,OAAOgW,SAAUzjB,KAAMgvB,SAzBVgB,GAGzB,GAAIvvB,OAAQ,OAAOgS,KAAK,IAAIpM,MAAM,wBAA0BrG,KAAO,KAAOyN,OAAOlO,KAAO,MAExF0Y,OAAOqG,SACP7L,UAlCiBA,KAAK,IAAIpM,MAAMyoB,IAAM,+BAnBxC3R,KAAK+S,QAAQ/H,QAAQ/d,GAAG,SAAU+S,KAAK+S,QAEpC/H,QAiHP,SAAS+G,aAAclvB,KAAMmZ;AAC3B,IAAI5G,IACJ,MAAQA,KAnKG4d,KAmKQpC,OAlKTjoB,OAASqqB,KAAKA,KAAKrqB,OAAS,GAAK,OAkKb9F,KAAKgN,MAAM,EAAGuF,IAAI,GAAGzM,UAAYyM,IAAI,IAAIwb,MAAMtN,MAnKjF,IAAe0P,KAoKX,IAAK5d,IAAK,OAAO4G,KACjBsU,IAAIwB,OAAO1c,IAAI,GAAIyb,IAAKzb,IAAI,GAAI4G,IAelC,SAASgW,OAAQnvB,KAAMyN,OAAQ0L,IAC7B,MAAMyW,KAAuB,YAAhBniB,OAAOlO,KAGd6wB,MAAQR,KAAOnC,IAAI4C,OAAS5C,IAAI2C,MAChCxC,MAAQgC,KAAOnC,IAAI6C,OAAS7C,IAAIG;;;AAGtC,IAAKwC,MAAO,OAAOjX,KAEnB,MAAM6K,MAAQvW,OAAOuW,MAAwB,cAAhBvW,OAAOlO,KAAuB8uB,MAAQC,QAAUL,MAK7E,SAASsC,QAAS5T,KAChB,OAAIA,IAAYxD,GAAGwD,KACdyT,WACLA,MAAMzmB,KAAK8jB,IAAKztB,KAAMgkB,KAAM7K,IADTA,KALjByU,OAASD,IAAKC,MAAMjkB,KAAK8jB,IAAKztB,KAAMyN,OAAOwW,IAAKxW,OAAOyW,IAAKqM,SAC3DA,QAAQ,MASf,SAASd,SAAUzvB,KAAMmd,KAAMhE;;;AAG7BsU,IAAIuB,KAAKhvB,MAAM,SAAU2c,KACvB,OAAKA,IACY,WAAbA,IAAIxU,KAA0BgR,GAAGwD,UACrC8Q,IAAI+C,MAAMxwB,KAAM,CAAEgkB,KAAM7G,KAAK6G,KAAMyM,WAAW,IAAQ,SAAU9T,IAAK+T,MACnE,GAAI/T,IAAK,OAAOxD,GAAGwD,KACnBwS,OAAOnvB,KAAMmd,KAAMhE,OAJJA,GAAG,WAU1B,SAASoW,SAAUhE,GAAIvrB,KAAM2wB,KAAMxX,IACjC,GAAInZ,OAAS2wB,KAAM,OAAOxX,GAAG,MAAM,GAEnCoS,GAAGqF,MAAM5wB,MAAM,SAAU2c,IAAKkU,IAC5B,OAAIlU,KAAoB,WAAbA,IAAIxU,MAAkC,UAAbwU,IAAIxU,KAAyBgR,GAAGwD,KAChEA,KAAOkU,GAAGC,cAAsBvB,SAAShE,GAAIpe,KAAKuhB,KAAK1uB,KAAM,MAAO2wB,KAAMxX,SAC9EA,GAAG,MAAM,MAIb,SAAS5P,QAET,SAAS4U,KAAMne,MACb,OAAOA,KCvUF,MAkGM+wB,2BAA8BC,mBACvC,MAAMC,WAAaxpB,QAAQypB,IAAuB,mBAAK,GACjDC,mBAAqB1pB,QAAQypB,IAA2B,uBAAK,GAC7DE,eAAiB3pB,QAAQypB,IAA2B,uBAAK;;AAE/D,SAAID,WAAWtc,SAAS,SACpBsc,WAAWtc,SAAS,SACpBwc,mBAAmBxc,SAAS,SAC5Bwc,mBAAmBxc,SAAS,SAC5Byc,eAAezc,SAAS,aACxByc,eAAezc,SAAS,iBAKxBlN,QAAQypB,IAAY,QAAKF,kBAAoB;;;EC/GxCK,QAAWC;;AAEpB,MAAMC,OAASD,SAAS3c,SAAS,eAC3B6c,iBACA9C,eAAK8C,iBAAUC,mBAASH,UAAUzC,QAAQ,8CAA+C,KAC/F,OAAO,IAAI1P,SAAQ,CAACxN,QAASyN;;AAEzB,GAAIkS,SAAS3c,SAAS,gBAClB,GAAI+c,mBAAW,GAAGH,uBAEd,YADA5f,QAAQ4f,aAIX,GAAIG,mBAAWH,QAEhB,YADA5f,QAAQ4f;;uDAKZ;MAAMI,SAAW,OAAOllB,KAAK6kB,UACvBM,OAAS,OAAOnlB,KAAK6kB,UACrBO,MAAQ,oCAAoCplB,KAAK6kB,UAIjDjd,OAAS0b,yBAAiBuB,SAAU,CAAEpZ,cADtB,GAAK,KAE3B,IAAItL;8CAEJ;MAAMklB,YAAe1qB,QACjBgY,OAAOhY;;AAkBX,GAhBAiN,OAAOhF,KAAK,QAASyiB;;AAEjBD,OACAjlB,OAASub,QAAQoJ,QACjB3kB,OAAOyC,KAAK,UAAU,KAClBsC,QAAQ4f,aAIZ3kB,OAAS0iB,0BAAkBiC,OAAQ,CAAEvN,KAAM,MAC3CpX,OAAOyC,KAAK,SAAS,KACjBsC,QAAQ4f,YAGhB3kB,OAAOyC,KAAK,QAASyiB,aAEjBH,UAAYC,OAAQ;;;AAGpB,MAAMG,aAAeJ,SACfK,iCAAuB,CAAEC,UAAW,GAAK,KACzCC,sBAAY,CAAED,UAAW,GAAK;6BAEpCF;aAAa1iB,KAAK,QAASyiB;;AAE3Bzd,OAAO4G,KAAK8W,cAAc9W,KAAKrO,aAG/ByH,OAAO4G,KAAKrO;;;GC9DxB,MAAMokB,iBAAmB9jB,OAAO8V,SAASvb,QAAQ0qB,SAASC,KAAK3D,MAAM,KAAK,IAAM;+BF8D1C;IAAC4D,YE5DnCtB,2BAA2BC,oBF4DQqB,YE3DZ3D,eAAK8C,iBAAU,SAAU;;AF6DhD/pB,QAAQypB,IAAqB,kBAAMxC,eAAK8C,iBAAU;;AAElD/pB,QAAQypB,IAAU,OAAMM;;KAEe9jB,IAAnCjG,QAAQypB,IAAqB,gBAC7BzpB,QAAQypB,IAAqB,gBAAImB,YAE3B5qB,QAAQypB,IAAqB,gBAAEnkB,WAAWslB,eAChD5qB,QAAQypB,IAAqB,gBAAI,CAC7BmB,eACG,IAAIC,IAAI7qB,QAAQypB,IAAqB,gBAAEzC,MAAM,OAClDC,KAAK;kEErEf;MAAM6D;;;;;;AAMS3vB,kBACP,MAgBM4vB,cAAgB,CAClB;AACA;;AAGA/yB,KAAKgzB,SACLD,cAAchuB;;AAEd,iBAAkB,0BAA2B,+BAG7CguB,cAAchuB,KAAK,mBAavB,MAAO,CAvCH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAkCA,sBAAsB,CA/BtB,2BACA,iBACA,oBA6BmDkqB,KAAK,OACxD,qBAAqB,CA5BO,qBA4BqBA,KAAK,UACnD8D,cAdH;AACA;AACA;AACA,yBAGA;AACA;AACA;;;;OAeGC,sBACP,OAAOhzB,KAAKizB;;;;;;OAQLC,2BAAgBnnB,OACvB,GAAqB,kBAAVA,MACP,MAAM,IAAIxC,UAAU,iDAAiD2E,OAAOnC,WAEhF/L,KAAKizB,aAAelnB;;;;;OAOxBgT,qBAAsB;;;;;;AAMtBA,4BAA4B/S;;;;AAIxB,GAAIimB,mBAAWhD,eAAK8C,iBAAU,aAC1B,OAAO9C,eAAK8C,iBAAU;;;;;WAO1B,GAAI/lB,OFrBc,CAACA,QACvB,IACI,OAAOmnB,QAAQ,IAAIlsB,IAAI+E,QAE3B,MACI,OAAO,IEgBMonB,CAAWpnB,OACpB,OAAOhM,KAAKqzB,oBFcUC,OAAO7yB;;AAClB,IAAIwG,IAAIxG,KAEhB6K,cAAgB,SAC3B,MAAMioB,QAAUtE,eAAK8C,iBAAU,iBAC/B,OAAO,IAAIrS,SAAQ,CAACxN,QAASyN,UACzB,MAAM6T,WAAa9K,QAAQ6K,SAGrBE,eAAkBvW,MACpBwW,WAAGH,QAAS,CAAEI,OAAO,EAAM3C,WAAW,IAAQ,KAC1CrR,OAAOzC;;;;AAIfsW,WAAW5jB,KAAK,QAAS6jB;;AAEzBD,WAAW5jB,KAAK,UAAU,KACtBsC,QAAQqhB,YAEZ,MAAM/hB,IAAMoiB,GAAGxsB,MAAMtG,IAAIL,KAAMqK;;AAEC,MAAxBA,SAASiG;;AAMbjG,SAAS0Q,KAAKgY;;AAEd1oB,SAAS8E,KAAK,QAAS6jB;;AANnB9T,OAAO,IAAI/Y,MAAM,2BAA2BkE,SAASiG,YAAY8iB,QAAQ,IAAM;wBASvFriB;IAAI5B,KAAK,QAAS6jB;;AAElBjiB,IAAIrC,WAAW,KAAW;;AAEtBqC,IAAI5H,UACJ6pB,eAAe,IAAI7sB,MAAM,2BEpDQktB,CAAmB9nB;;;;;;;;;AAWxD,GAJAA;;;;AClHD,SAAS+nB,aACZ,OAAO9E,eAAKK,kBAAQ0E,uMAAiC,KAAM,KAAM,ODiHnDD,IAIL9B,mBAAWjmB,OACZ,MAAM,IAAIpF,MAAM,wBAAwBoF;6BAG5C;MAAMioB,SAAW,CACbrC,QAAQ3C,eAAKjjB,MAAO,gBACpB4lB,QAAQ3C,eAAKjjB,MAAO,iBACpB4lB,QAAQ3C,eAAKjjB,MAAO,wBAEpBslB,2BAA2BC,mBAC3B0C,SAASlvB,KAAK6sB,QAAQ3C,eAAKjjB,MAAO;;;;AAMtC,aAHqB0T,QAAQqB,IAAIkT,WAGnB9gB;;;OAKlB4L,kBAAkB/S,OACd,MAAMkoB,SAAWlsB,QAAQypB,IAAqB,iBAC1CxC,eAAKjnB,QAAQypB,IAAU,MAAKM,iBAAU;6CAErCE;mBAAWiC,WACZC,kBAAUD;;AAGT,gBAAgBlnB,KAAKhB,SACtBA,MAAQ,UAAUA,SAEtB,MAAMvL,IAAM,IAAIwG,aAAI+E,OACdooB,SAAW3zB,IAAIkN,SAASqhB,MAAM,KAAKhO,MACzC,IAAKoT,SACD,MAAM,IAAIxtB,MAAM,sBAAsBnG,IAAIkN,YAE9C,MAAM0mB,WAAa,GAAGH,YAAYE;wCAElC;GAAInC,mBAAWoC,YACX,OAAOD;oBAGX;GAAqB,UAAjB3zB,IAAIiL,SACJ,IAEI,aFjKckJ,OEgKMnU,IAAIkN,SFhKFR,OEgKYknB,WF/JvC,IAAI3U,SAAQ,CAACxN,QAASyN,UACzB2U,eAAO1f,QAASjN,QACRA,MACAgY,OAAOhY,OAGX6oB,gBAAQ5b,OAAQzH,QAASxF;;AAEjBA;;AAEAgY,OAAOhY,OAGXuK,oBEmJOkiB,SAEX,MAAOzsB,OACH,MAAM,IAAIf,MAAM,sCAAsC2tB,KAAKC,UAAU7sB,eAKzE,IAEI,YFrJY,EAAClH,IAAK4zB,aACvB,IAAI3U,SAAQ,CAACxN,QAASyN,UACzB,MAAMnH,OAASqX,0BAAkBwE,YACjC7b,OAAO5I,KAAK,QAAS+P,QACrBiU,GAAGxsB,MACEtG,IAAIL,KAAMqK,WACX,GAA4B,MAAxBA,SAASiG,WAKT,OAJAyH,OAAOgV,aACP7N,OAAO,IAAI/Y;;AAEX,2BAA2BkE,SAASiG,YAAY8iB,QAAQ,IAAM;;8CAKlE/oB;SAAS0Q,KAAKhD;;AAEdA,OAAO5I,KAAK,UAAU,KAClB4I,OAAOgV,QACPtb;;AAGJpH,SAAS8E,KAAK,SAAUjI;;AAEpB6Q,OAAOgV,QACP7N,OAAOhY;uBAIVgD,GAAG,SAAUhD,QACd6Q,OAAOgV,QACP7N,OAAOhY,aEqHG8sB,CAAazoB,MAAOqoB,YACnBD,SAEX,MAAOzsB,OACH,MAAM,IAAIf,MAAM,4BAA4B2tB,KAAKC,UAAU7sB,UF9K9C,IAACiN,OAAQzH,QIJtC,MAAMunB,iBAAiB9tB;;;;;;;;;;;;;;;AAmBtB/G,YAAYuO,QAAU,uBACfA,8CAdS,0CAOD;;;;;;mBAgBDzG,MAAOgtB,aAAe,aAC5B/zB,OAAOC,OAAO,IAAI6zB,SAAS/sB,MAAMyG,SAAU,CACjDwmB,cAAejtB,MACfgtB,aAAAA;;;;;;;;;;;;;;;;ACiGH,MAAM9tB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDLhH,YAAYmK,QAAU,kSACjB6qB,OCxJN,SAASC,gBACRC,OAAOpuB,IACNA,IAAS,cADHzC,OAENA,OAAS,CACR,iBACA,iBACA,kBALK8wB,OAONA,OAAS,CACR,iBACA,iBACA,mBAEE,GAbmBC,IAcvBA,KAAkB,EAdK3kB,MAevBA,MAAkB,KAfK4kB,QAgBvBA,QAAkB,KAhBKC,aAiBvBA,cAAkB,EAjBKC,YAkBvBA,YAAkB,GAlBKC,gBAmBvBA,gBAAkB,MACf,IACE/kB,QACJA,MAAQ2kB,IACLK,iBACAC,kBAE2B,aAA3BjlB,MAAMzQ,YAAYU,OACrB+P,MAAQ,IAAIA;MAGPklB,eAAkBC,YACG,iBAAfA,WACH,CAAEA,YAEHvyB,MAAMsB,QAAQixB,YAAcA,WAAa,CAAEA,kBAG5C,CACNV,MAAO,CACNpuB,IAAAA,IACAzC,OAAQsxB,eAAetxB,QACvB8wB,OAAQQ,eAAeR,SAExBC,IAAAA,IACA3kB,MAAAA,MACA4kB,QAAAA,QACAC,aAAAA,aACAC,YAAAA,YACAC,gBAAAA,iBDwGaP,CAAe9qB,SAE5BpJ,OAAOC,OAAOb,KAAM60B;;;;KAOjBa,iBACI11B,KAAKi1B,IACT7tB,4BACAF;;;;;;;KAUJyuB,WAAWZ,MAAOa,SAAW,mBACvB1yB,MAAMsB,QAAQuwB,QAA2B,IAAjBA,MAAM1uB,cAC3BuvB;OAKDb,MADO5d,KAAKyM,MAAMzM,KAAK0e,SAAWd,MAAM1uB;;;;;;;KAWhDyF,QAAQ9B;;GAEHhK,KAAKm1B,oBACDn1B,KAAK81B,qBAAqB9rB;IAI9B0rB,IACHA,IADGplB,MAEHA,MAFG4kB,QAGHA,SACGl1B,YACG,IAAI0f,SAAQ,CAACxN,QAASyN,gBACtB7R,QAAU,cACA,oCAAwC9F,QAAQ0qB,SAASC,kCAIrEuC;UACHpnB,QAAQioB,OAASb,SAGlBt0B,OAAOC,OAAOmJ,QAAS,CACtBsG,MAAAA,MACAxC,QAAAA,UAGD4nB,IAAI50B,IAAIkJ,SAASgsB;;AAGflrB,SAAWkrB,WACXjlB,WAAEA,YAAgBjG,SAClBmrB,YAAcnrB,SAASgD,QAAQ,oBAE5BnG,SACe,MAAfoJ,WACHpJ,MAAQ,IAAIf,MAAO,mCAAkCmK,cAC3C,qBAAsB/D,KAAKipB,eACrCtuB,MAAQ,IAAIf,MAAO,iEAAgEqvB,gBAEhFtuB,aACHmD,SAAS+T,cACTc,OAAO+U,SAASwB,OAAOvuB,MAAOmD,WAI/BA,SAAS0T,YAAY,YACjB2X,QAAU,GACdrrB,SAASH,GAAG,QAASyb,OAAU+P,SAAW/P,QAC1Ctb,SAASH,GAAG,OAAO,SAEjBuH,QAAQqiB,KAAKtwB,MAAMkyB,UAClB,MAAOxuB,OACRgY,OAAO+U,SAASwB,OAAOvuB,MAAOmD,kBAG9BH,GAAG,SAAShD,OAASgY,OAAO+U,SAASwB,OAAOvuB;;;;;;;;gCAYtBqC,aACtBosB,UAAWC;;AAIdD,gBAAkB1W,oEAAO,uBACzB2W,qBAAuB3W,oEAAO,uCAAmC4W,QAChE,MAAO3uB,aACF,IAAIf,MAAM,wLAIjBwvB;UAAUE,QAAQC,IAAIF,uBAEhB51B,IAAO,OAAMT,KAAKi1B,IAAM,IAAM,QAAQjrB,QAAQuC,OAAOvC,QAAQ0D,WAC/D8oB;;;AAMFA,QAFGx2B,KAAKq1B,iBAAmD,mBAAzBr1B,KAAKq1B,sBAEvBr1B,KAAKq1B,wBAGLe,UAAUE,QAAQG,OAAO,CACxCtzB,KAAiBuzB,SAASvzB,KAAKsR,OAAOzU,KAAKo1B,aAAe,IAC1DuB,gBAAiBD,SAASC,gBAC1BtD,qBAAuBqD,SAASrD,iBAChCuD,SAAiBF,SAASE,iBAItB9xB,WAAa0xB,QAAQK;;SAGrB/xB,KAAKgyB,aAAc,oCAAwC9uB,QAAQ0qB,SAASC,QAG9E3yB,KAAKk1B,QAAS,OAEhBA,QADqBl1B,KAAKk1B,QAAQlG,MAAM,KAChB/tB,KAAI81B,kBACnBx2B,KAAMwL,OAAWgrB,UAAU5oB,OAAO6gB,MAAM,WACzC,CACNzuB,KAAQA,KAAK4N,OACbpC,MAAQA,MAAQA,MAAMoC,OAAS,GAC/BkE,OAAQrI,QAAQuC,eAGbzH,KAAKkyB,aAAa9B;GAIElrB,QAAQ0D,KAAKwH,SAAS,WAEzB;;IAEnB+hB,iBAAmB,KAEvBnyB,KAAK6F,GAAG,YAAYG,WACO,MAAtBA,SAASosB,UAAoBpsB,SAASrK,QAAUA,MACnDw2B,iBAAmBnsB;MAKfhG,KAAKqyB,wBAAuB,GAClCryB,KAAK6F,GAAG,WAAWmB,UAClBA,QAAQsrB,wBAIFtyB,KAAKuyB,KAAK52B,IAAK,CACpB62B,UAAW,eACXC,QAAW,MAEX,MAAO5vB;IAILsvB,iBAAkB;;MAEfO,UAAY,IAAI5wB,MAAO,mCAAkCqwB,iBAAiBC,kBAChFM,UAAU7C,aAAe,CACxB5jB,WAAYkmB,iBAAiBC,SAC7BppB,QAAY,CACXoD,SAAU+lB,iBAAiBnpB,UAAUoD,WAGjCwjB,SAASwB,OAAOsB,UAAWA,UAAU7C,oBAErC,IAAI/tB,MAAM,wCAEX;;MAEA9B,KAAK2yB,oBAAoB,QACd,mDACA;MAIX3sB,eAAiBhG,KAAKuyB,KAAK52B,IAAK,CACrC62B,UAAW,eACXC,QAAW,UAGPzsB,SAAS4sB,WACP,IAAI9wB,MAAO,mCAAkCkE,SAASosB;MAIvDS,mBAAqB7sB,SAAS8sB,WAErB9sB,SAASgD,UAAU,iBAAmB,IAErCoH,SAAS;;WAGhBqf,KAAKtwB,MAAM0zB,cACjB,MAAOE,kBACF,IAAIjxB,MAAO,0BAAyBixB,WAAWzpB,eAEhD;;MAGL0pB,iBADqBhzB,KAAKizB,WACNC,MAAM,+BACvBC;;AAIHA,SAFGH,UAEQA,UAAU,GAAG3pB,aAGPrJ,KAAKozB,UAAS;;;MAGxBC,WAAahwB,SAASiwB,cAAc,cACtCD,WACIA,WAAWE,YAIZlwB,SAASmwB,KAAKD;;eAKf9D,KAAKtwB,MAAMg0B,UACjB,MAAOJ,kBACF,IAAIjxB,MAAO,0BAAyBixB,WAAWzpB,8BAA8B6pB,UAAU7nB,UAAU,EAAG,sBAMzGomB,eACGA,QAAQhJ;;;;;;;;KAajB+K,WAAWC,SAAU7xB,SAEnBouB,QACEyD,UAAW/C,YAEb51B,aACC44B,UACE9xB,KAAM+xB,WAGN14B;MAOG,CACNuM,KALYrJ,MAAMsB,QAAQixB,YACxBz1B,KAAK21B,WAAWF,WAAYA,WAAW,IACvCA,WAIFiD,QAAAA;;;;;;;;kBAYWnyB,MAAOzB,KAAO,EAAG0B,KAAO,QAChC+F,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,MAAO,UAC/CvyB,OAASD,OAAO9B,YACTjE,KAAK8L,QAAQ,CAClBS,KAAAA,KACAmB,KAAMgrB,QAAQnyB,MAAOzB,KAAM0B,gBAI9B5F,OAAOC,OAAOmF,OAAQ,CACrBW,IAAK3G,KACLuG,MAAAA,MACAzB,KAAAA,KACA0B,KAAAA,OAGMR;;;;;;;;;2BAYgBO,MAAOzB,KAAO,EAAG0B,KAAO,QAC3CR,aAAehG,KAAKgG,OAAOO,MAAOzB,KAAM0B,WAErCR,OAAOlB,MAAQkB,OAAO7B,aACtB6B,OACNA,aAAehG,KAAKgG,OAAOO,MAAOP,OAAOlB,KAAO,EAAG0B;;;;;;uBAUnC9D,UACb6J,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,MAAO,sBAEzCxyB,OAAO9B,YACPjE,KAAK8L,QAAQ,CAClBS,KAAAA,KACAmB,KAAMgrB,QACLh2B,gBAAgBC,KACbD,KAAKpC,IACJoC;;;;;;;;wBAcWrC,IAAKyE,KAAO,EAAG0B,KAAO,IAClCnG,eAAeD,MACpBC,IAAMD,IAAIU,IAAI,CAAER,IAAKD,WAClBkM,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,MAAO,gBAC/CvyB,OAASD,OAAO9B,YACTjE,KAAK8L,QAAQ,CAClBS,KAAAA,KACAmB,KAAMgrB,QAAQr4B,IAAIC,GAAIwE,KAAM0B,gBAI/B5F,OAAOC,OAAOmF,OAAQ,CACrBW,IAAO3G,KACPuG,MAAOlG,IACPyE,KAAAA,KACA0B,KAAAA,OAGMR;;;;;;mBASM2yB,YACTpsB,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,MAAO,eAEzC51B,KAAKsB,YACLjE,KAAK8L,QAAQ,CAClBS,KAAAA,KACAmB,KAAMgrB,QAAQC;;;;;+BAWZpsB,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,MAAO,gCAGzCv4B,KAAK8L,QAAQ,CAClBS,KAAAA,KACAmB,KAAMgrB;CAEN,MAAO/wB,YACFA,iBAAiB+sB,UACtB,MAAM/sB,YACDmD,SAAWnD,MAAMgtB,iBAClB7pB,UAAoC,MAAxBA,SAASiG,WACzB,MAAMpJ,YACDrH,KAAQ,MAAOs4B,KAAK9tB,SAASgD,QAAQoD,WAAa,IAAI,MACxD2nB,MAAMv4B,IACT,MAAMo0B,SAASwB,OAAO,IAAItvB,MAAM,gBAAiBkE,uBACrC9K,KAAK84B,QAAQx4B;;;;;;KAU5By4B,qBAAqB52B,aACd62B,kBAAoB72B,MAAMrC,KAAKkC;GAGX,SAAtBg3B,kBAA8B;;OAMjB72B,MAAMO,KAAKa,MAIb,IACN,YAID;;;;;MAcP01B,gBANgC,KACvB;;KACA;;IACA;;IACA,OAEkCD,0BAExCC,gBAEK,GAAEA,uBAIJD;;;;;;KAQRE,YAAY/2B,UACPA,iBAAiBD,MAAO,KAI1BF,WAHGuK,KAAEA,KAAFmsB,QAAQA,SAAav2B,MAAMS,QAC3B5C,KAAKu4B,WAAW,SAAU,aAC1Bv4B,KAAKu4B,WAAW,SAAU;OAK7Bv2B,UADGG,MAAMS,QACG5C,KAAK+4B,qBAAqB52B,OAG1BA,MAAMrC,KAAKkC,UAGhB,OAAMhC,KAAKi1B,IAAM,IAAM,QAAQ1oB,QAAUpK,MAAMS,QACpD81B,QAAQv2B,MAAMO,KAAKa,MAAOvB,WAC1B02B,QAAQv2B,MAAMO,KAAKa,MAAOpB,MAAM7B,GAAI0B,kBAElC,IAAI4E,MAAM;;;;;KAQjBuyB,oBAAoBh3B,UACfA,iBAAiBD,MAAO,KACvBqK,KAAEA,KAAFmsB,QAAQA,SAAav2B,MAAMS,QAC5B5C,KAAKu4B,WAAW,SAAU,aAC1Bv4B,KAAKu4B,WAAW,SAAU;MAGrB,OAAMv4B,KAAKi1B,IAAM,IAAM,QAAQ1oB,QAAUpK,MAAMS,QACpD81B,QAAQv2B,MAAMO,KAAKa,MAAOpB,MAAMrC,KAAKkC,WACrC02B,QAAQv2B,MAAMO,KAAKa,MAAOpB,MAAM7B,GAAI6B,MAAMrC,KAAKkC,kBAE7C,IAAI4E,MAAM;;;;;KAQjBwyB,oBAAoBj3B,YACbA,iBAAiBD,OAAWC,MAAMS,eACjC,IAAIgE,MAAM,6CAGb2F,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,SAAU,aAClDc,QAAW,OAAMr5B,KAAKi1B,IAAM,IAAM,QAAQ1oB,OAC1C+sB,YAAcn3B,MAAMrC,KAAKkC,UACzBu3B,SAAW;;AAEXC,SAAWx5B,KAAK+4B,qBAAqB52B;OAEtCo3B,SAASx0B,KAAKs0B,QAAUX,QAAQv2B,MAAMO,KAAKa,MAAOi2B;AAGlDD,SAASx0B,KAAKs0B,QAAUX,QAAQv2B,MAAMO,KAAKa,MAAO+1B;AAG9B,SAAhBA,cACHC,SAASx0B,KAAKs0B,QAAUX,QAAQv2B,MAAMO,KAAKa,MAAO,SAClDg2B,SAASx0B,KAAKs0B,QAAUX,QAAQv2B,MAAMO,KAAKa,MAAO;AAI/B,SAAhB+1B,aACHC,SAASx0B,KAAKs0B,QAAUX,QAAQv2B,MAAMO,KAAKa,MAAQ,GAAE+1B,qBAI/C,IAAK,IAAIzG,IAAI0G;;;;;KAQrBE,YAAYt3B,UACPA,iBAAiBD,QAAUC,MAAMS,QAAS,KACzC2J,KAAEA,KAAFmsB,QAAQA,SAAa14B,KAAKu4B,WAAW,SAAU,mBAE3C,OAAMv4B,KAAKi1B,IAAM,IAAM,QAAQ1oB,OACpCmsB,QAAQv2B,MAAMO,KAAKa,MAAOpB,MAAM7B,GAAI6B,MAAMrC,KAAKkC,iBAE7C,IAAI4E,MAAM,qEA7nBZC,cArFN,MAAM4xB;;;;;;;;cAQSlyB,MAAOzB,KAAO,EAAG0B,KAAO,UAC7B,+BAA8BD,cAAczB,OAAO0B,KAAO,SAAWA,KAAO;;;;;;yBASjEkzB,MAAO50B,KAAO,SACzB,gCAA+B40B,cAAc50B;;;;;wBAQnC6zB,cACV,gBAAeA;;;;;iBAQZA,cACH,gBAAeA;;;;;;sBASPgB,QAAS33B,iBACjB,cAAa23B,iBAAiB33B;;;;;;;qBAUvB23B,QAAS70B,KAAM9C,iBACtB,cAAa23B,WAAW70B,QAAQ9C;;;;;;;sBAUxB23B,QAAS70B,KAAM9C,iBACvB,cAAa23B,WAAW70B,SAAS9C;;;;uCAQlC;;;;;;;MEtGIuD,SAAW,CACvB5E,QAAWP,IAAIM,MAAMC,QACrBP,IAAWA,IAAIM,MAAMN,IACrBmB,SAAWnB,IAAIM,MAAMa,SACrBC,OAAWpB,IAAIM,MAAMc,OACrBC,OAAWrB,IAAIM,MAAMe,OACrBC,UAAWtB,IAAIM,MAAMgB,UACrBC,MAAWvB,IAAIM,MAAMiB,MACrBC,SAAWxB,IAAIM,MAAMkB"}