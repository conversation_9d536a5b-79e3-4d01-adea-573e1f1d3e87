/**
 * Example: Using nhentai-api with serverless Chrome (AWS Lambda)
 * 
 * This example shows how to use the nhentai-api with a custom Puppeteer launch function
 * for serverless environments like AWS Lambda using @sparticuz/chromium.
 */

import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import chromium from '@sparticuz/chromium';
import { API } from 'nhentai-api';

// Use stealth plugin
puppeteer.use(StealthPlugin());

// Create API instance with custom puppeteerLaunch function
const api = new API({
  usePuppeteer: true,
  puppeteerLaunch: async () => {
    return await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
    });
  },
});

// Example usage
async function searchExample() {
  try {
    console.log('Searching for manga...');
    const search = await api.search('example', 1);
    console.log(`Found ${search.books.length} results on page ${search.page} of ${search.pages}`);
    
    if (search.books.length > 0) {
      const firstBook = search.books[0];
      console.log(`First result: ${firstBook.title.pretty} (ID: ${firstBook.id})`);
    }
  } catch (error) {
    console.error('Search failed:', error.message);
  }
}

// Example with different serverless configurations
const apiWithCustomConfig = new API({
  usePuppeteer: true,
  puppeteerLaunch: async () => {
    // Custom configuration for different serverless providers
    const isAWSLambda = process.env.AWS_LAMBDA_FUNCTION_NAME;
    const isVercel = process.env.VERCEL;
    
    let launchOptions = {
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
    };
    
    if (isAWSLambda) {
      // AWS Lambda specific optimizations
      launchOptions.args.push('--single-process');
      launchOptions.args.push('--no-zygote');
    }
    
    if (isVercel) {
      // Vercel specific optimizations
      launchOptions.args.push('--disable-dev-shm-usage');
    }
    
    return await puppeteer.launch(launchOptions);
  },
});

// Example with error handling and retries
const apiWithRetries = new API({
  usePuppeteer: true,
  puppeteerLaunch: async () => {
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        return await puppeteer.launch({
          args: chromium.args,
          defaultViewport: chromium.defaultViewport,
          executablePath: await chromium.executablePath(),
          headless: chromium.headless,
        });
      } catch (error) {
        attempts++;
        console.warn(`Puppeteer launch attempt ${attempts} failed:`, error.message);
        
        if (attempts >= maxAttempts) {
          throw error;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
      }
    }
  },
});

// Export for use in serverless functions
export { api, apiWithCustomConfig, apiWithRetries, searchExample };

// If running directly (not imported)
if (import.meta.url === `file://${process.argv[1]}`) {
  searchExample();
}
